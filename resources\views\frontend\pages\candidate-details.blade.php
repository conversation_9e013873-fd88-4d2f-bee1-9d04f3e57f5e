@extends('frontend.layouts.app')

@section('description')
    @php
    $data = metaData('candidate-details');
    @endphp
    {{ $data->description }}
@endsection
@section('og:image')
    {{ asset($candidate->candidate ? $candidate->candidate->photo : $candidate->image) }}
@endsection
@section('title')
    Detail {{ $candidate->name }}
@endsection

@section('main')
    <div class="breadcrumbs breadcrumbs-height">
        <div class="container">
            <div class="breadcrumb-menu">
                <h6 class="f-size-18 m-0">{{ auth('user')->check() ? $candidate->name : maskFullName($candidate->name) }}</h6>
                <ul>
                    <li><a href="{{ route('website.home') }}">Beranda</a></li>
                    <li>/</li>
                    <li><a href="{{ route('website.candidate') }}">Pencaker</a></li>
                    <li>/</li>
                    <li>{{ auth('user')->check() ? $candidate->name : mask<PERSON>ull<PERSON><PERSON>($candidate->name) }}</li>
                </ul>
            </div>
        </div>
    </div>

    <!-- Candidate Profile Header -->
    <div class="rt-pt-50">
        <div class="container">
            <div class="row">
                <div class="col-xl-12">
                    <div class="card jobcardStyle1 body-24 border border-gray-50">
                        <div class="card-body">
                            <div class="rt-single-icon-box flex-column flex-lg-row">
                                <div class="icon-thumb rt-mb-lg-20">
                                    <img src="{{ asset($candidate->candidate ? $candidate->candidate->photo : $candidate->image) }}"
                                        alt="photo" draggable="false" class="object-fit-contain" style="width: 120px; height: 120px; border-radius: 50%; cursor: pointer;"
                                        onclick="showImageModal('{{ asset($candidate->candidate ? $candidate->candidate->photo : $candidate->image) }}')">
                                </div>
                                <div class="iconbox-content">
                                    <div class="post-info2">
                                        <div class="post-main-title2">
                                            <h2 class="mb-2">
                                                {{ auth('user')->check() ? $candidate->name : maskFullName($candidate->name) }}
                                            </h2>
                                            <p class="text-muted mb-1">
                                                @if ($candidate->candidate)
                                                    {{ $candidate->candidate->profession ? $candidate->candidate->profession->name : '' }}
                                                @endif
                                            </p>
                                            <p class="text-muted">
                                                <i class="ph-map-pin me-1"></i>
                                                {{ $candidate->exact_location ? $candidate->exact_location: $candidate->full_address }}
                                            </p>
                                        </div>
                                    </div>
                                </div>
                                <div class="iconbox-extra align-self-start align-self-lg-center rt-pt-lg-20 flex-md-row flex-column">
                                    @if (auth('user')->check() && auth()->user()->role == 'company')
                                        <div class="me-3 mb-2 mb-lg-0">
                                            @if ($candidate->candidate ? $candidate->candidate->bookmark_candidates_count : '')
                                                <button type="button" class="btn btn-primary" onclick="confirmRemoveBookmark('{{ $candidate->name }}', {{ $candidate->candidate->id }})">
                                                    <i class="ph-bookmark-simple-fill me-1"></i>
                                                    Tersimpan
                                                </button>
                                            @else
                                                <button type="button" class="btn btn-outline-primary" onclick="showBookmarkModal({{ $candidate->candidate->id }}, '{{ $candidate->name }}')">
                                                    <i class="ph-bookmark-simple me-1"></i>
                                                    Simpan
                                                </button>
                                            @endif
                                        </div>
                                    @endif
                                    @if ($candidate->candidate && $candidate->candidate->cv && $candidate->candidate->cv_visibility)
                                        <div>
                                            <a href="" class="btn btn-primary"
                                                download="{{ asset('frontend') }}/assets/images/all-img/single-candidate-1.jpg"
                                                href="{{ asset('frontend') }}/assets/images/all-img/single-candidate-1.jpg">
                                                <i class="ph-cloud-arrow-down me-1"></i>
                                                {{ __('download_cv') }}
                                            </a>
                                        </div>
                                    @endif
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!--Single job content Area-->
    <div class="single-job-content rt-pt-50">
        <div class="container">
            <div class="row">
                <div class="col-lg-7 rt-mb-lg-30">
                    <div class="body-font-1 ft-wt-5 rt-mb-20">Tentang Saya</div>
                    <div class="body-font-3 text-gray-500 rt-mb-40">
                        {!! $candidate->candidate ? $candidate->candidate->bio : '' !!}
                    </div>

                    <!-- Riwayat Pendidikan -->
                    <div class="body-font-1 ft-wt-5 rt-mb-20">Riwayat Pendidikan</div>
                    <div class="rt-mb-40">
                        @if ($candidate->candidate && $candidate->candidate->educations && $candidate->candidate->educations->count() > 0)
                            @foreach ($candidate->candidate->educations->sortByDesc('year') as $education)
                                <div class="card border-0 shadow-sm rt-mb-20">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h5 class="card-title mb-1">{{ $education->degree }}</h5>
                                                <p class="text-muted mb-1">{{ $education->level }}</p>
                                                <small class="text-primary">{{ $education->year }}</small>
                                            </div>
                                        </div>
                                        @if ($education->notes)
                                            <p class="card-text mt-2 text-gray-600">{{ $education->notes }}</p>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="text-gray-500">Tidak ada data pendidikan</div>
                        @endif
                    </div>

                    <!-- Pengalaman Kerja -->
                    <div class="body-font-1 ft-wt-5 rt-mb-20">Pengalaman Kerja</div>
                    <div class="rt-mb-40">
                        @if ($candidate->candidate && $candidate->candidate->experiences && $candidate->candidate->experiences->count() > 0)
                            @foreach ($candidate->candidate->experiences->sortByDesc('start') as $experience)
                                <div class="card border-0 shadow-sm rt-mb-20">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h5 class="card-title mb-1">{{ $experience->designation }}</h5>
                                                <p class="text-muted mb-1">{{ $experience->company }}@if($experience->department) / {{ $experience->department }}@endif</p>
                                                <small class="text-primary">
                                                    {{ $experience->formatted_start }} -
                                                    {{ $experience->currently_working ? 'Saat ini' : $experience->formatted_end }}
                                                </small>
                                            </div>
                                        </div>
                                        @if ($experience->responsibilities)
                                            <p class="card-text mt-2 text-gray-600">{{ $experience->responsibilities }}</p>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="text-gray-500">Tidak ada data pengalaman kerja</div>
                        @endif
                    </div>

                    <!-- Sertifikasi -->
                    <div class="body-font-1 ft-wt-5 rt-mb-20">Sertifikasi</div>
                    <div class="rt-mb-40">
                        @if ($candidate->candidate && $candidate->candidate->certifications && $candidate->candidate->certifications->count() > 0)
                            @foreach ($candidate->candidate->certifications->sortByDesc('issue_date') as $certification)
                                <div class="card border-0 shadow-sm rt-mb-20">
                                    <div class="card-body">
                                        <div class="d-flex justify-content-between align-items-start">
                                            <div>
                                                <h5 class="card-title mb-1">{{ $certification->name }}</h5>
                                                <p class="text-muted mb-1">{{ $certification->organization }}</p>
                                                <small class="text-primary">
                                                    {{ $certification->formatted_issue_date }}
                                                    @if ($certification->expiration_date)
                                                        - {{ $certification->formatted_expiration_date }}
                                                    @endif
                                                </small>
                                            </div>
                                        </div>
                                        @if ($certification->credential_id)
                                            <p class="card-text mt-2 text-gray-600">ID: {{ $certification->credential_id }}</p>
                                        @endif
                                        @if ($certification->credential_url)
                                            <a href="{{ $certification->credential_url }}" target="_blank" class="btn btn-sm btn-outline-primary mt-2">
                                                Lihat Kredensial
                                            </a>
                                        @endif
                                    </div>
                                </div>
                            @endforeach
                        @else
                            <div class="text-gray-500">Tidak ada data sertifikasi</div>
                        @endif
                    </div>
                </div>
                <div class="col-lg-5">
                    <div class="cadidate-details-sidebar">
                        <div class="sidebar-widget">
                            <div class="row">
                                @if ($candidate?->candidate?->birth_date)
                                    <div class="col-sm-4">
                                        <div class="icon-box">
                                            <div class="icon-img">
                                                <x-svg.birth-date-icon />
                                            </div>
                                            <h3 class="sub-title">{{ __('date_of_birth') }}</h3>
                                            <h2 class="title">
                                                @php
                                                    $months = [
                                                        1 => 'Januari', 2 => 'Februari', 3 => 'Maret', 4 => 'April',
                                                        5 => 'Mei', 6 => 'Juni', 7 => 'Juli', 8 => 'Agustus',
                                                        9 => 'September', 10 => 'Oktober', 11 => 'November', 12 => 'Desember'
                                                    ];
                                                    $date = \Carbon\Carbon::parse($candidate->candidate->birth_date);
                                                    $age = $date->age;
                                                @endphp
                                                {{ $date->day }} {{ $months[$date->month] }} {{ $date->year }} ({{ $age }} Tahun)
                                            </h2>
                                        </div>
                                    </div>
                                @endif
                                @if ($candidate?->contactInfo?->country?->name)
                                    <div class="col-sm-4">
                                        <div class="icon-box">
                                            <div class="icon-img">
                                                <x-svg.fold-icon />
                                            </div>
                                            <h3 class="sub-title">{{ __('country') }}</h3>
                                            <h2 class="title">{{ $candidate->contactInfo->country->name }}
                                            </h2>
                                        </div>
                                    </div>
                                @endif
                                @if ($candidate?->candidate?->marital_status)
                                    <div class="col-sm-4">
                                        <div class="icon-box">
                                            <div class="icon-img">
                                                <x-svg.board-icon />
                                            </div>
                                            <h3 class="sub-title">{{ __('marital_status') }}</h3>
                                            <h2 class="title">
                                                @if($candidate->candidate->marital_status == 'married')
                                                    Kawin
                                                @elseif($candidate->candidate->marital_status == 'single')
                                                    Lajang
                                                @else
                                                    {{ ucfirst($candidate->candidate->marital_status) }}
                                                @endif
                                            </h2>
                                        </div>
                                    </div>
                                @endif
                                @if ($candidate?->candidate?->gender)
                                    <div class="col-sm-4">
                                        <div class="icon-box">
                                            <div class="icon-img">
                                                <x-svg.gender />
                                            </div>
                                            <h3 class="sub-title">{{ __('gender') }}</h3>
                                            <h2 class="title">
                                                @if($candidate->candidate->gender == 'male')
                                                    Laki-laki
                                                @elseif($candidate->candidate->gender == 'female')
                                                    Perempuan
                                                @else
                                                    {{ ucfirst($candidate->candidate->gender) }}
                                                @endif
                                            </h2>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                        <div class="sidebar-widget">
                            <div class="contact">
                                <h2 class="title">{{ __('contact_information') }}</h2>
                                @if ($candidate->website)
                                    <div class="contact-icon-box">
                                        <div class="icon-img">
                                            <x-svg.globe-icon />
                                        </div>

                                        <div class="info">
                                            <h3 class="subtitle">
                                                {{ __('website') }}
                                            </h3>
                                            <h2 class="title">{{ $candidate->website }}</h2>
                                        </div>
                                    </div>
                                @endif
                                <div class="devider"></div>
                                <div class="contact-icon-box">
                                    <div class="icon-img">
                                        <x-svg.location2-icon />
                                    </div>
                                    <div class="info">
                                        <h3 class="subtitle">Domisili</h3>
                                        <h2 class="title">
                                            {{ $candidate->exact_location ? $candidate->exact_location : ($candidate->full_address ?: 'Tidak Diketahui') }}
                                        </h2>
                                    </div>
                                </div>
                                <p class="address">{{ $candidate?->contactInfo?->address ?? ''}}</p>
                                @if (($candidate->contactInfo && $candidate->contactInfo->phone) || $candidate->contactInfo->secondary_phone)
                                    <div class="devider"></div>
                                    <div class="contact-icon-box">
                                        <div class="icon-img">
                                            <x-svg.telephone-icon />
                                        </div>
                                        <div class="info">
                                            @if ($candidate->contactInfo->phone)
                                                <h3 class="subtitle">{{ __('phone') }}</h3>
                                                <h2 class="title">{{ auth('user')->check() ? $candidate->contactInfo->phone : '0' . str_repeat('*', strlen($candidate->contactInfo->phone) - 3) . substr($candidate->contactInfo->phone, -2) }}</h2>
                                            @endif
                                            @if ($candidate->contactInfo->secondary_phone)
                                                <h3 class="subtitle">{{ __('secondary_phone') }}</h3>
                                                <h2 class="title">
                                                    {{ auth('user')->check() ? $candidate->contactInfo->secondary_phone : '0' . str_repeat('*', strlen($candidate->contactInfo->secondary_phone) - 3) . substr($candidate->contactInfo->secondary_phone, -2) }}</h2>
                                            @endif
                                        </div>
                                    </div>
                                @endif
                                @if ($candidate?->contactInfo?->email)
                                    <div class="devider"></div>
                                    <div class="contact-icon-box">
                                        <div class="icon-img">
                                            <x-svg.envelope-icon height="32" width="32" />
                                        </div>
                                        <div class="info">
                                            <h3 class="subtitle">{{ __('email_address') }}</h3>
                                            <h2 class="title">{{ auth('user')->check() ? $candidate->contactInfo->email : substr($candidate->contactInfo->email, 0, 2) . str_repeat('*', strpos($candidate->contactInfo->email, '@') - 2) . '@' . substr(strstr($candidate->contactInfo->email, '@'), 1) }}</h2>
                                        </div>
                                    </div>
                                @endif
                            </div>
                        </div>
                        @if ((isset($candidate?->socialInfo) && $candidate?->socialInfo) && isset($candidate->socialInfo->facebook) || isset($candidate->socialInfo->twitter) || isset($candidate->socialInfo->instagram) || isset($candidate->socialInfo->youtube))
                            <div class="p-32 border border-1.5 border-primary-50 rt-rounded-12  max-536">
                                <div class="row g-0">
                                    <div class="col-xl-5 d-flex align-items-center">
                                        <div class="f-size-18 text-gray-600">
                                            {{ __('social_profile') }}:
                                        </div>
                                    </div>
                                    <div class="col-xl-7 col-12 rt-pt-lg-10 text-xxl-end text-start">
                                        <ul class="rt-list gap-8">
                                            @if ($candidate?->socialInfo?->facebook)
                                                <li class="d-inline-block">
                                                    <a href="{{ $candidate?->socialInfo?->facebook ?? '' }}"
                                                        target="__black"
                                                        class="icon-44 bg-gray-10 bg-primary-50 text-primary-500 hover:border-primary-500">
                                                        <x-svg.facebook-icon fill="currentColor" />
                                                    </a>
                                                </li>
                                            @endif
                                            @if ($candidate?->socialInfo?->twitter)
                                                <li class="d-inline-block">
                                                    <a href="{{ $candidate?->socialInfo?->twitter ?? '' }}"
                                                        class="icon-44 bg-gray-10 bg-primary-50 text-primary-500 hover:border-primary-500">
                                                        <x-svg.twitter-icon />
                                                    </a>
                                                </li>
                                            @endif
                                            @if ($candidate?->socialInfo?->instagram)
                                                <li class="d-inline-block">
                                                    <a href="{{ $candidate?->socialInfo?->instagram ?? '' }}"
                                                        class="icon-44 bg-gray-10 bg-primary-50 text-primary-500 hover:border-primary-500">
                                                        <x-svg.instagram-icon />
                                                    </a>
                                                </li>
                                            @endif
                                            @if ($candidate?->socialInfo?->linkedin)
                                                <li class="d-inline-block">
                                                    <a href="{{ $candidate?->socialInfo?->linkedin ?? '' }}"
                                                        class="icon-44 bg-gray-10 bg-primary-50 text-primary-500 hover:border-primary-500">
                                                        <x-svg.linkedin-icon />
                                                    </a>
                                                </li>
                                            @endif
                                            @if ($candidate?->socialInfo?->youtube)
                                                <li class="d-inline-block">
                                                    <a href="{{ $candidate?->socialInfo?->youtube ?? '' }}"
                                                        class="icon-44 bg-gray-10 bg-primary-50 text-primary-500 hover:border-primary-500">
                                                        <x-svg.youtube-icon />
                                                    </a>
                                                </li>
                                            @endif
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        @endif
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Pencaker Terkait -->
    @if(isset($relatedCandidates) && $relatedCandidates->count() > 0)
    <div class="rt-pt-50" id="related_candidates_section">
        <div class="container">
            <div class="row">
                <div class="col-12">
                    <div class="body-font-1 ft-wt-5 rt-mb-30 text-center">
                        Pencaker Terkait <span class="tw-text-primary-500" id="related-candidate-count">({{ $relatedCandidates->total() }})</span>
                    </div>

                    <!-- Candidate List Container -->
                    <div class="row" id="related-candidates-container">
                        @include('frontend.pages.partials.related-candidates', ['relatedCandidates' => $relatedCandidates])
                    </div>

                    <!-- Loading Indicator -->
                    <div id="related-candidates-loading" class="tw-hidden tw-flex tw-justify-center tw-items-center tw-py-8">
                        <div class="tw-flex tw-items-center tw-gap-2">
                            <div class="tw-w-5 tw-h-5 tw-border-2 tw-border-t-primary-500 tw-border-r-primary-500 tw-border-b-transparent tw-border-l-transparent tw-rounded-full tw-animate-spin"></div>
                            <span class="tw-text-gray-600">Memuat pencaker terkait...</span>
                        </div>
                    </div>

                    <!-- Pagination -->
                    <div class="tw-mt-8 tw-flex tw-justify-center" id="related-candidates-pagination-container">
                        {{ $relatedCandidates->appends(['related_candidates' => true])->links('vendor.pagination.frontend') }}
                    </div>
                </div>
            </div>
        </div>
    </div>
    @endif

    <div class="rt-spacer-100 rt-spacer-md-50"></div>

    <!-- Image Modal -->
    <div class="modal fade" id="imageModal" tabindex="-1" aria-labelledby="imageModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="imageModalLabel">Foto Profil</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body text-center">
                    <img id="modalImage" src="" alt="Foto Profil" class="img-fluid" style="max-height: 70vh;">
                </div>
            </div>
        </div>
    </div>

    <!-- Modal Bookmark Kategori -->
    <div class="modal fade" id="bookmarkModal" tabindex="-1" aria-labelledby="bookmarkModalLabel" aria-hidden="true">
        <div class="modal-dialog modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="bookmarkModalLabel">Simpan ke Kategori</h5>
                    <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
                </div>
                <div class="modal-body">
                    <div class="mb-3">
                        <label for="bookmark_category" class="form-label">Pilih Kategori</label>
                        <select class="form-select" id="bookmark_category">
                            <option value="">Pilih kategori...</option>
                        </select>
                    </div>
                    <div class="mb-3">
                        <label for="new_category_name" class="form-label">Atau buat kategori baru</label>
                        <input type="text" class="form-control" id="new_category_name" placeholder="Nama kategori baru">
                    </div>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Batal</button>
                    <button type="button" id="saveBookmarkBtn" class="btn btn-primary">Simpan</button>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('script')
<script>
    function showImageModal(imageSrc) {
        document.getElementById('modalImage').src = imageSrc;
        var imageModal = new bootstrap.Modal(document.getElementById('imageModal'));
        imageModal.show();
    }

    function confirmRemoveBookmark(candidateName, candidateId) {
        Swal.fire({
            title: 'Konfirmasi Hapus',
            text: `Anda yakin ingin menghapus ${candidateName} dari daftar Bookmark?`,
            icon: 'warning',
            showCancelButton: true,
            confirmButtonColor: '#d33',
            cancelButtonColor: '#6c757d',
            confirmButtonText: 'Ya',
            cancelButtonText: 'Batal'
        }).then((result) => {
            if (result.isConfirmed) {
                // Create and submit form to remove bookmark
                const form = document.createElement('form');
                form.method = 'POST';
                form.action = `{{ route('company.companybookmarkcandidate', '') }}/${candidateId}`;

                const csrfToken = document.createElement('input');
                csrfToken.type = 'hidden';
                csrfToken.name = '_token';
                csrfToken.value = '{{ csrf_token() }}';

                form.appendChild(csrfToken);
                document.body.appendChild(form);
                form.submit();
            }
        });
    }

    let currentCandidateId = null;
    let currentCandidateName = '';

    function showBookmarkModal(candidateId, candidateName) {
        currentCandidateId = candidateId;
        currentCandidateName = candidateName;

        // Load categories
        loadBookmarkCategories();

        // Show modal
        $('#bookmarkModal').modal('show');
    }

    function loadBookmarkCategories() {
        $.ajax({
            url: "{{ route('company.bookmark.category.index') }}",
            type: "GET",
            data: { ajax: true },
            success: function(response) {
                const select = $('#bookmark_category');
                select.empty();
                select.append('<option value="">Pilih kategori...</option>');

                $.each(response, function(index, category) {
                    select.append('<option value="' + category.id + '">' + category.name + '</option>');
                });
            },
            error: function() {
                toastr.error('Gagal memuat kategori');
            }
        });
    }

    $('#saveBookmarkBtn').on('click', function() {
        const selectedCategory = $('#bookmark_category').val();
        const newCategoryName = $('#new_category_name').val().trim();

        if (!selectedCategory && !newCategoryName) {
            toastr.error('Pilih kategori atau buat kategori baru');
            return;
        }

        // Show loading
        $(this).html('<span class="spinner-border spinner-border-sm me-2"></span>Menyimpan...').prop('disabled', true);

        if (newCategoryName) {
            // Create new category first, then bookmark
            createCategoryAndBookmark(newCategoryName);
        } else {
            // Bookmark with existing category
            bookmarkCandidate(selectedCategory);
        }
    });

    function createCategoryAndBookmark(categoryName) {
        $.ajax({
            url: "{{ route('company.bookmark.category.store') }}",
            type: "POST",
            data: {
                name: categoryName,
                _token: "{{ csrf_token() }}"
            },
            success: function(response) {
                // Bookmark with new category
                bookmarkCandidate(response.id);
            },
            error: function() {
                toastr.error('Gagal membuat kategori baru');
                resetBookmarkButton();
            }
        });
    }

    function bookmarkCandidate(categoryId) {
        $.ajax({
            url: "{{ route('company.companybookmarkcandidate', '') }}/" + currentCandidateId,
            type: "POST",
            data: {
                cat: categoryId,
                _token: "{{ csrf_token() }}"
            },
            success: function(response) {
                $('#bookmarkModal').modal('hide');
                toastr.success('Kandidat berhasil disimpan ke bookmark');

                // Update button to "Tersimpan" state
                location.reload(); // Simple reload to update the UI
            },
            error: function() {
                toastr.error('Gagal menyimpan kandidat');
                resetBookmarkButton();
            }
        });
    }

    function resetBookmarkButton() {
        $('#saveBookmarkBtn').html('Simpan').prop('disabled', false);
    }

    // Reset modal when closed
    $('#bookmarkModal').on('hidden.bs.modal', function() {
        $('#bookmark_category').val('');
        $('#new_category_name').val('');
        resetBookmarkButton();
    });

    // Related candidates pagination
    $(document).ready(function() {
        const pageSize = 3;

        // Handle pagination clicks for related candidates
        $(document).on('click', '#related-candidates-pagination-container .page-link', function(e) {
            e.preventDefault();

            const url = $(this).attr('href');
            if (url) {
                loadRelatedCandidates(url);

                // Update URL without refreshing the page
                const newUrl = updateURLParameter(window.location.href, 'page', getParameterByName('page', url));
                window.history.pushState({ path: newUrl }, '', newUrl);
            }

            return false;
        });

        // Function to load related candidates via AJAX
        function loadRelatedCandidates(url) {
            // Show loading indicator
            $('#related-candidates-loading').removeClass('tw-hidden');

            // Add per_page parameter if not present
            url = updateURLParameter(url, 'per_page', pageSize);
            // Add related_candidates parameter
            url = updateURLParameter(url, 'related_candidates', true);

            $.ajax({
                url: url,
                type: 'GET',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(response) {
                    // Update candidate list
                    $('#related-candidates-container').html(response.html);

                    // Update pagination
                    $('#related-candidates-pagination-container').html(response.pagination);

                    // Update candidate count
                    $('#related-candidate-count').text('(' + response.total + ')');

                    // Hide loading indicator
                    $('#related-candidates-loading').addClass('tw-hidden');

                    // Scroll to candidate list
                    $('#related_candidates_section').get(0).scrollIntoView({ behavior: 'smooth', block: 'start' });
                },
                error: function(xhr) {
                    console.error('Error loading related candidates:', xhr);
                    $('#related-candidates-loading').addClass('tw-hidden');
                }
            });
        }

        // Helper function to get URL parameter
        function getParameterByName(name, url) {
            if (!url) url = window.location.href;
            name = name.replace(/[\[\]]/g, '\\$&');
            const regex = new RegExp('[?&]' + name + '(=([^&#]*)|&|#|$)');
            const results = regex.exec(url);
            if (!results) return null;
            if (!results[2]) return '';
            return decodeURIComponent(results[2].replace(/\+/g, ' '));
        }

        // Helper function to update URL parameter
        function updateURLParameter(url, param, paramVal) {
            let newAdditionalURL = '';
            let tempArray = url.split('?');
            let baseURL = tempArray[0];
            let additionalURL = tempArray[1];
            let temp = '';
            if (additionalURL) {
                tempArray = additionalURL.split('&');
                for (let i = 0; i < tempArray.length; i++) {
                    if (tempArray[i].split('=')[0] != param) {
                        newAdditionalURL += temp + tempArray[i];
                        temp = '&';
                    }
                }
            }
            const rows_txt = temp + '' + param + '=' + paramVal;
            return baseURL + '?' + newAdditionalURL + rows_txt;
        }
    });
</script>
@endsection
