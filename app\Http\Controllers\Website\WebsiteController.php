<?php

namespace App\Http\Controllers\Website;

use App\Http\Controllers\Controller;
use App\Http\Traits\CandidateAble;
use App\Http\Traits\HasCountryBasedJobs;
use App\Http\Traits\JobAble;
use App\Http\Traits\ResetCvViewsHistoryTrait;
use App\Models\Candidate;
use App\Models\CandidateCvView;
use App\Models\CandidateResume;
use App\Models\Company;
use App\Models\Education;
use App\Models\Experience;
use App\Models\Job;
use App\Models\Profession;
use App\Models\Skill;
use App\Models\Tag;
use App\Models\User;
use App\Models\City;
use App\Notifications\Website\Candidate\ApplyJobNotification;
use App\Notifications\Website\Candidate\BookmarkJobNotification;
use App\Notifications\Website\Candidate\ProfileViewedNotification;
use App\Services\Website\Candidate\CandidateProfileDetailsService;
use App\Services\Website\Company\CompanyDetailsService;
use App\Services\Website\Company\CompanyListService;
use App\Services\Website\IndexPageService;
use App\Services\Website\Job\JobListService;
use App\Services\Website\PricePlanService;
use App\Services\Website\PrivacyPolicyService;
use App\Services\Website\RefundPolicyService;
use App\Services\Website\TermsConditionService;
use Carbon\Carbon;
use Illuminate\Contracts\Support\Renderable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Notification;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Modules\Blog\Entities\Post;
use Modules\Blog\Entities\PostCategory;
use Modules\Blog\Entities\PostComment;
use Modules\Currency\Entities\Currency as CurrencyModel;
use Modules\Faq\Entities\Faq;
use Modules\Faq\Entities\FaqCategory;
use Modules\Language\Entities\Language;
use Modules\Location\Entities\Country;
use Modules\Plan\Entities\Plan;
use Modules\Testimonial\Entities\Testimonial;
use Srmklive\PayPal\Services\PayPal;
use Stevebauman\Location\Facades\Location;

class WebsiteController extends Controller
{
    use CandidateAble, HasCountryBasedJobs, JobAble, ResetCvViewsHistoryTrait;

    public $setting;

    public function __construct()
    {
        $this->setting = loadSetting(); // see helpers.php
    }

    /**
     * Show the application dashboard.
     *
     * @return Renderable
     */
    public function dashboard()
    {
        try {
            if (auth('user')->check() && authUser()->role == 'candidate') {
                return redirect()->route('candidate.dashboard');
            } elseif (auth('user')->check() && authUser()->role == 'company') {
                storePlanInformation();

                return redirect()->route('company.dashboard');
            }

            return redirect('login');
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Notification mark as read
     *
     * @param  Request  $request
     * @return void
     */
    public function notificationRead()
    {
        try {
            foreach (auth()->user()->unreadNotifications as $notification) {
                $notification->markAsRead();
            }

            return response()->json(true);
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Home page view
     *
     * @param  Request  $request
     * @return void
     */
    public function index()
    {
        try {
            // Ambil data untuk halaman index
            $indexPageService = new IndexPageService();
            $data = $indexPageService->execute();

            // Ambil 10 lowongan terbatas
            $jobListService = new JobListService();
            $limitedJobs = $jobListService->getLimitedJobs(10);
            $data['limited_jobs'] = $limitedJobs;

            // Ambil semua kecamatan dari database dan hitung jumlah lowongan di setiap kecamatan
            $districts = Job::select('locality as value', DB::raw('COUNT(*) as job_count'))
                ->groupBy('locality')
                ->orderBy('locality')
                ->get();

            $data['locality'] = $districts; // Menambahkan kecamatan dengan jumlah lowongan ke data yang dikirim ke view

            // Render view berdasarkan pengaturan landing page
            if ($this->setting->landing_page == 2) {
                return view('frontend.pages.index-2', $data);
            } elseif ($this->setting->landing_page == 3) {
                return view('frontend.pages.index-3', $data);
            } else {
                return view('frontend.pages.index', $data);
            }
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: ' . $e->getMessage());
            return back();
        }
    }


    /**
     * Terms and condition page view
     *
     * @param  Request  $request
     * @return void
     */
    public function termsCondition()
    {
        try {
            $data = (new TermsConditionService())->execute();

            return view('frontend.pages.terms-condition', $data);
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Privacy policy page view
     *
     * @param  Request  $request
     * @return void
     */
    public function privacyPolicy()
    {
        try {
            $data = (new PrivacyPolicyService())->execute();

            return view('frontend.pages.privacy-policy', $data);
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Refund policy page view
     *
     * @param  Request  $request
     * @return void
     */
    public function refundPolicy()
    {

        try {
            $data = (new RefundPolicyService())->execute();

            return view('frontend.pages.refund-policy', $data);

        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Job page view
     *
     * @return void
     */
    public function jobs(Request $request)
    {
        try {
            // Check if we have a category parameter or slug
            $categorySlug = $request->kategori ?? null;

            // If we're coming from a category URL, use that instead
            if ($request->route('category')) {
                $categorySlug = $request->route('category');
            }

            // Get data based on whether we have a category or not
            if ($categorySlug) {
                // Check if category exists
                $category = \App\Models\JobCategory::where('slug', $categorySlug)->first();

                if (!$category) {
                    // If category doesn't exist, show available categories
                    $availableCategories = \App\Models\JobCategory::withCount(['jobs' => function ($query) {
                        return $query->where('status', 'active')
                            ->where('deadline', '>=', now()->toDateString());
                    }])
                    ->join('job_category_translations', function($join) {
                        $join->on('job_categories.id', '=', 'job_category_translations.job_category_id')
                             ->where('job_category_translations.locale', '=', app()->getLocale());
                    })
                    ->orderBy('job_category_translations.name')
                    ->select('job_categories.*')
                    ->take(6)
                    ->get();

                    // Show error page for non-existent category
                    return response()->view('errors.category-not-found', [
                        'cms_setting' => loadSetting(),
                        'slug' => $categorySlug,
                        'availableCategories' => $availableCategories
                    ], 404);
                }

                // Get jobs for this category
                $data = (new JobListService())->categoryJobs($request, $categorySlug);

                // Add category info to data
                $data['selected_category'] = $category;
                $data['category_name'] = $category->name;
            } else {
                // Get all jobs (no category filter)
                $data = (new JobListService())->jobs($request);
            }

            // Ensure that all required keys are present in the data array
            $data['limited_jobs'] = $data['limited_jobs'] ?? ($data['jobs'] ?? collect());
            $data['all_jobs'] = $data['all_jobs'] ?? $data['limited_jobs'];
            $data['mix_jobs'] = $data['mix_jobs'] ?? $data['limited_jobs']; // Ensure mix_jobs is set

            // For adding currency code
            $current_currency = currentCurrency(); // Ensure this function returns the current currency

            // Check if this is an AJAX request
            if ($request->ajax() || $request->header('X-Requested-With') == 'XMLHttpRequest') {
                return view('frontend.pages.jobs', array_merge($data, ['current_currency' => $current_currency]))->render();
            }

            // Pass the currency and jobs data to the view
            return view('frontend.pages.jobs', array_merge($data, ['current_currency' => $current_currency]));
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());
            return back();
        }
    }

    public function loadmore(Request $request)
    {
        try {
            $data = (new JobListService())->loadMore($request);

            return view('components.website.job.load-more-jobs', compact('data'));
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Job categories page view
     *
     * @return void
     */
    public function jobCategories(Request $request)
    {
        try {
            // Simple test first
            $categories = collect([
                (object) [
                    'id' => 1,
                    'slug' => 'teknologi',
                    'icon' => 'fas fa-laptop',
                    'name' => 'Teknologi',
                    'jobs_count' => 10
                ],
                (object) [
                    'id' => 2,
                    'slug' => 'marketing',
                    'icon' => 'fas fa-bullhorn',
                    'name' => 'Marketing',
                    'jobs_count' => 5
                ]
            ]);

            $transformedCategories = $categories;

            // Urutkan berdasarkan nama
            $transformedCategories = $transformedCategories->sortBy('name');

            // Hitung total jobs
            $totalJobs = $transformedCategories->sum('jobs_count');

            // Handle pagination with per_page parameter
            $perPage = $request->per_page ?? 9;
            $page = $request->page ?? 1;
            $paginatedCategories = $transformedCategories->forPage($page, $perPage);

            // Create a custom paginator
            $paginator = new \Illuminate\Pagination\LengthAwarePaginator(
                $paginatedCategories,
                $transformedCategories->count(),
                $perPage,
                $page,
                ['path' => $request->url(), 'query' => $request->query()]
            );
            $paginator->onEachSide(1); // Show 1 page on each side of the current page

            // Get additional data for filtering
            $jobListService = new JobListService();
            $filterData = $jobListService->getFilterData();

            // Siapkan data untuk view
            $data = [
                'categories' => $paginator,
                'all_categories' => $transformedCategories,
                'total_categories' => $transformedCategories->count(),
                'total_jobs' => $totalJobs,
                'countries' => $filterData['countries'] ?? [],
                'job_roles' => $filterData['job_roles'] ?? [],
                'max_salary' => $filterData['max_salary'] ?? 0,
                'min_salary' => $filterData['min_salary'] ?? 0,
                'experiences' => $filterData['experiences'] ?? [],
                'educations' => $filterData['educations'] ?? [],
                'job_types' => $filterData['job_types'] ?? [],
                'popularTags' => $filterData['popularTags'] ?? []
            ];

            // Check if this is an AJAX request
            if ($request->ajax() || $request->header('X-Requested-With') == 'XMLHttpRequest') {
                return view('frontend.pages.job-categories', $data)->render();
            }

            // Gunakan view baru yang menggunakan layout app
            return view('frontend.pages.job-categories', $data);
        } catch (\Exception $e) {
            \Log::error("Error pada jobCategories: {$e->getMessage()}");
            \Log::error("Stack trace: {$e->getTraceAsString()}");

            // Tampilkan halaman dengan kategori kosong daripada error
            return view('frontend.pages.job-categories', [
                'categories' => collect(),
                'total_categories' => 0,
                'total_jobs' => 0
            ]);
        }
    }

    // Metode jobsCategory telah digabungkan ke dalam metode jobs

    /**
     * Job details page view
     *
     * @param  Request  $request
     * @param  string  $slug
     * @return void
     */
    public function jobDetails(Request $request, Job $job)
    {
        try {
            if ($job->status == 'pending') {
                if (! auth('admin')->check()) {
                    abort_if(! auth('user')->check(), 404);
                    abort_if(authUser()->role != 'company', 404);
                    abort_if(currentCompany()->id != $job->company_id, 404);
                }
            }

            $data = $this->getJobDetails($job);
            $data['questions'] = $job->questions;

            // Handle AJAX request for related jobs pagination
            if ($request->ajax() && $request->has('related_jobs')) {
                $perPage = $request->per_page ?? 3;
                $page = $request->page ?? 1;

                // Manually paginate the collection
                $collection = collect($data['related_jobs']);
                $total = $collection->count();

                // Get current page items
                $currentPageItems = $collection->forPage($page, $perPage);

                // Create a custom paginator
                $paginator = new \Illuminate\Pagination\LengthAwarePaginator(
                    $currentPageItems,
                    $total,
                    $perPage,
                    $page,
                    ['path' => $request->url(), 'query' => $request->query()]
                );

                $html = view('frontend.pages.partials.related-jobs', ['related_jobs' => $paginator])->render();
                $pagination = view('vendor.pagination.frontend', ['paginator' => $paginator])->render();

                return response()->json([
                    'html' => $html,
                    'pagination' => $pagination,
                    'total' => $total
                ]);
            }

            // Manually paginate related jobs for initial load
            $collection = collect($data['related_jobs']);
            $perPage = $request->per_page ?? 3;
            $page = $request->page ?? 1;
            $total = $collection->count();

            // Get current page items
            $currentPageItems = $collection->forPage($page, $perPage);

            // Create a custom paginator
            $data['related_jobs'] = new \Illuminate\Pagination\LengthAwarePaginator(
                $currentPageItems,
                $total,
                $perPage,
                $page,
                ['path' => $request->url(), 'query' => $request->query()]
            );

            return view('frontend.pages.job-details', $data);
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Company list page view with AJAX support
     *
     * @return \Illuminate\Http\Response
     */
    public function company(Request $request)
    {
        try {
            // Get company list data
            $data = (new CompanyListService())->execute($request);

            // Add alphabet filter
            $alphabet = range('A', 'Z');
            $data['alphabet'] = $alphabet;

            // Filters are now handled in CompanyListService

            // Filter by alphabet if requested
            if ($request->has('alphabet') && $request->alphabet != '') {
                $letter = $request->alphabet;

                // Jika kita menggunakan filter pada collection, kita perlu mengubahnya kembali menjadi paginator
                $filtered = $data['companies']->getCollection()->filter(function($company) use ($letter) {
                    return strtoupper(substr($company->user->name, 0, 1)) === $letter;
                });

                // Buat paginator baru dengan hasil filter
                $currentPage = $data['companies']->currentPage();
                $perPage = $data['companies']->perPage();

                // Hitung total items setelah filter
                $total = $filtered->count();

                // Slice collection untuk halaman saat ini
                $items = $filtered->slice(($currentPage - 1) * $perPage, $perPage)->values();

                // Buat paginator baru
                $data['companies'] = new \Illuminate\Pagination\LengthAwarePaginator(
                    $items,
                    $total,
                    $perPage,
                    $currentPage,
                    ['path' => $request->url(), 'query' => $request->query()]
                );
            }

            // Handle AJAX request
            if ($request->ajax() || $request->header('X-Requested-With') == 'XMLHttpRequest') {
                $html = view('frontend.pages.partials.company-list', ['companies' => $data['companies']])->render();
                $pagination = view('vendor.pagination.frontend', ['paginator' => $data['companies']])->render();

                return response()->json([
                    'html' => $html,
                    'pagination' => $pagination,
                    'total' => $data['companies']->total()
                ]);
            }

            // Use the new template
            return view('frontend.pages.employees-new', $data);
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());
            return back();
        }
    }

    /**
     * Candidate page view
     *
     * @return void
     */
    public function candidates(Request $request)
    {
        abort_if(auth('user')->check() && authUser()->role == 'candidate', 404);

        try {
            // Ambil keahlian unik dari candidate experiences
            $data['keahlian'] = \DB::table('candidate_experiences')
                ->whereNotNull('designation')
                ->where('designation', '!=', '')
                ->distinct()
                ->pluck('designation')
                ->sort()
                ->values();

            $data['candidates'] = $this->getCandidates($request);
            $data['experiences'] = Experience::all();
            $data['educations'] = Education::all();
            $data['popularTags'] = Tag::popular()
                ->withCount('tags')
                ->latest('tags_count')
                ->get()
                ->take(10);

            // reset candidate cv views history
            $this->reset();

            // Check if request is AJAX
            if ($request->ajax() || $request->header('X-Requested-With') == 'XMLHttpRequest') {
                return view('frontend.pages.partials.candidates-content', $data)->render();
            }

            return view('frontend.pages.candidates', $data);
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Candidate details page view
     *
     * @param  string  $username
     * @return void
     */
    public function candidateDetails(Request $request, $username)
    {
        try {
            // Check if user is logged in
            if (!auth('user')->check()) {
                return redirect()->route('login')->with('error', 'Anda harus login sebagai perusahaan untuk melihat profil kandidat.');
            }

            // Check if user is company
            if (auth('user')->user()->role !== 'company') {
                return redirect()->route('login')->with('error', 'Hanya perusahaan yang dapat melihat profil kandidat.');
            }

            $candidate = User::where('username', $username)
                ->where('role', 'candidate')
                ->with('candidate', 'contactInfo', 'socialInfo')
                ->firstOrFail();

            if ($request->ajax) {
                return response()->json($candidate);
            }

            // Get related candidates based on age and education
            $relatedCandidates = $this->getRelatedCandidates($candidate, $request);

            // Handle AJAX request for related candidates
            if ($request->ajax() && $request->has('related_candidates')) {
                $html = view('frontend.pages.partials.related-candidates', compact('relatedCandidates'))->render();
                $pagination = $relatedCandidates->appends(['related_candidates' => true])->links('vendor.pagination.frontend')->render();

                return response()->json([
                    'html' => $html,
                    'pagination' => $pagination,
                    'total' => $relatedCandidates->total()
                ]);
            }

            return view('frontend.pages.candidate-details', compact('candidate', 'relatedCandidates'));
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Candidate profile details
     *
     * @return Response
     */
    public function candidateProfileDetails(Request $request)
    {
        try {
            if (! auth('user')->check()) {
                return response()->json([
                    'message' => __('if_you_perform_this_action_you_need_to_login_your_account_first_do_you_want_to_login_now'),
                    'success' => false,
                ]);
            }

            $user = authUser();

            if ($user->role != 'company') {
                return response()->json([
                    'message' => __('you_are_not_authorized_to_perform_this_action'),
                    'success' => false,
                ]);
            }

            $data = (new CandidateProfileDetailsService())->execute($request);

            return response()->json($data);
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());
            return back();
        }
    }

    /**
     * Candidate application profile details
     *
     * @return Response
     */
    public function candidateApplicationProfileDetails(Request $request)
    {

        try {
            // Periksa apakah pengguna sudah login
            if (!auth('user')->check()) {
                return response()->json([
                    'success' => false,
                    'message' => __('if_you_perform_this_action_you_need_to_login_your_account_first_do_you_want_to_login_now'),
                ]);
            }

            $user = authUser();
            $candidate = User::where('username', $request->username)
                ->where('role', 'candidate')
                ->firstOrFail();

            // Periksa otorisasi: hanya perusahaan yang dapat melihat profil kandidat
            if ($user->role != 'company') {
                return response()->json([
                    'success' => false,
                    'message' => __('you_are_not_authorized_to_perform_this_action'),
                ]);
            }

            // Periksa apakah perusahaan memiliki hak untuk melihat profil kandidat ini
            // Perusahaan hanya dapat melihat profil kandidat yang melamar ke lowongan mereka
            $company_id = $user->company->id;
            $hasApplied = DB::table('applied_jobs')
                ->whereIn('job_id', function ($query) use ($company_id) {
                    $query->select('id')
                        ->from('jobs')
                        ->where('company_id', $company_id);
                })
                ->where('candidate_id', $candidate->candidate->id)
                ->exists();

            // Jika kandidat belum melamar ke perusahaan ini, tolak akses
            if (!$hasApplied && !auth('admin')->check()) {
                return response()->json([
                    'success' => false,
                    'message' => __('you_are_not_authorized_to_view_this_candidate_profile'),
                ]);
            }

            // Ambil data kandidat dengan relasi yang diperlukan
            $candidate = User::where('username', $request->username)
                ->with([
                    'contactInfo' => function ($query) {
                        $query->select('id', 'user_id', 'phone', 'secondary_phone', 'email', 'secondary_email');
                    },
                    'socialInfo',
                    'candidate' => function ($query) {
                        $query->with('experiences', 'educations', 'experience', 'coverLetter', 'education', 'profession', 'languages:id,name', 'skills');
                    },
                ])
                ->firstOrFail();

            // Format tanggal lahir
            $candidate->candidate->birth_date = formatTanggalIndonesia($candidate->candidate->birth_date, 'j F Y');

            // Ambil bahasa dan skills
            $languages = $candidate->candidate
                ->languages()
                ->pluck('name')
                ->toArray();
            $candidate_languages = $languages ? implode(', ', $languages) : '';

            $skills = $candidate->candidate->skills->pluck('name');
            $candidate_skills = $skills ? implode(', ', json_decode(json_encode($skills))) : '';

            // Hapus informasi sensitif dari respons
            if (isset($candidate->candidate->ktp_file)) {
                unset($candidate->candidate->ktp_file);
            }

            // Tambahkan informasi from_recruitment dan job_id ke respons
            $from_recruitment = $request->has('from_recruitment') ? $request->from_recruitment : false;
            $job_id = $request->has('job_id') ? $request->job_id : null;

            return response()->json([
                'success' => true,
                'data' => $candidate,
                'skills' => $candidate_skills,
                'languages' => $candidate_languages,
                'from_recruitment' => $from_recruitment,
                'job_id' => $job_id
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
            ]);
        }
    }

    /**
     * Send notification when candidate profile is viewed
     *
     * @param Request $request
     * @return Response
     */
    public function candidateProfileViewed(Request $request)
    {
        try {
            // Periksa apakah pengguna sudah login
            if (!auth('user')->check()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Anda harus login terlebih dahulu',
                ]);
            }

            $user = authUser();

            // Periksa otorisasi: hanya perusahaan yang dapat melihat profil kandidat
            if ($user->role != 'company') {
                return response()->json([
                    'success' => false,
                    'message' => 'Anda tidak memiliki akses untuk melakukan tindakan ini',
                ]);
            }

            // Jika job_id tidak ada, kembalikan error
            if (!$request->job_id) {
                return response()->json([
                    'success' => false,
                    'message' => 'ID pekerjaan diperlukan',
                ]);
            }

            // Ambil data job
            $job = Job::find($request->job_id);
            if (!$job) {
                return response()->json([
                    'success' => false,
                    'message' => 'Pekerjaan tidak ditemukan',
                ]);
            }

            // Jika melihat profil
            if ($request->type == 'profile') {
                $candidate = User::where('username', $request->username)
                    ->where('role', 'candidate')
                    ->firstOrFail();

                // Periksa apakah kandidat melamar pekerjaan ini
                $appliedJob = AppliedJob::where('job_id', $job->id)
                    ->where('candidate_id', $candidate->candidate->id)
                    ->first();

                if (!$appliedJob) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Kandidat tidak melamar pekerjaan ini',
                    ]);
                }

                // Periksa apakah notifikasi sudah pernah dikirim untuk lamaran ini
                $notificationSent = DB::table('notifications')
                    ->where('type', 'App\Notifications\Website\Candidate\ProfileViewedNotification')
                    ->where('notifiable_id', $candidate->id)
                    ->where('data', 'like', '%"job_id":' . $job->id . '%')
                    ->where('data', 'like', '%"type":"profile"%')
                    ->exists();

                // Jika notifikasi belum pernah dikirim untuk lamaran ini
                if (!$notificationSent) {
                    // Kirim notifikasi ke kandidat
                    $candidate->notify(new ProfileViewedNotification($user, $candidate, 'profile', $job));
                }
            }
            // Jika melihat CV
            else if ($request->type == 'cv') {
                $resume = CandidateResume::findOrFail($request->resume_id);

                // Periksa apakah resume memiliki candidate
                if (!$resume->candidate) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Data kandidat tidak ditemukan untuk CV ini',
                    ], 404);
                }

                // Periksa apakah candidate memiliki user
                if (!$resume->candidate->user) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Data user tidak ditemukan untuk kandidat ini',
                    ], 404);
                }

                $candidate = $resume->candidate->user;

                // Periksa apakah kandidat melamar pekerjaan ini
                $appliedJob = AppliedJob::where('job_id', $job->id)
                    ->where('candidate_id', $resume->candidate_id)
                    ->first();

                if (!$appliedJob) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Kandidat tidak melamar pekerjaan ini',
                    ]);
                }

                // Periksa apakah notifikasi sudah pernah dikirim untuk lamaran ini
                $notificationSent = DB::table('notifications')
                    ->where('type', 'App\Notifications\Website\Candidate\ProfileViewedNotification')
                    ->where('notifiable_id', $candidate->id)
                    ->where('data', 'like', '%"job_id":' . $job->id . '%')
                    ->where('data', 'like', '%"type":"cv"%')
                    ->exists();

                // Jika notifikasi belum pernah dikirim untuk lamaran ini
                if (!$notificationSent) {
                    // Kirim notifikasi ke kandidat
                    $candidate->notify(new ProfileViewedNotification($user, $candidate, 'cv', $job));
                }
            }

            return response()->json([
                'success' => true,
                'message' => 'Notifikasi berhasil dikirim',
            ]);
        } catch (\Exception $e) {
            return response()->json([
                'success' => false,
                'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
            ], 500);
        }
    }

    /**
     * Candidate download cv
     *
     * @return void
     */
    public function candidateDownloadCv(CandidateResume $resume)
    {
        try {
            // Periksa apakah user login (baik sebagai user biasa atau admin)
            if (!auth()->check() && !auth('admin')->check()) {
                flashError('Anda harus login untuk mengunduh CV');
                return redirect()->route('login');
            }

            $hasAccess = false;

            // Periksa apakah user adalah admin
            if (auth('admin')->check()) {
                // Admin dapat mengunduh semua CV
                $hasAccess = true;
            } else if (auth()->check()) {
                // Periksa role user biasa
                if (auth()->user()->role == 'admin') {
                    // Admin dapat mengunduh semua CV
                    $hasAccess = true;
                } else if (auth()->user()->role == 'company' && auth()->user()->company) {
                    // Perusahaan hanya dapat mengunduh CV pencaker yang melamar ke lowongan mereka
                    $company_id = auth()->user()->company->id;
                    $hasAccess = DB::table('applied_jobs')
                        ->whereIn('job_id', function ($query) use ($company_id) {
                            $query->select('id')
                                ->from('jobs')
                                ->where('company_id', $company_id);
                        })
                        ->where('candidate_id', $resume->candidate_id)
                        ->exists();
                } else if (auth()->user()->role == 'candidate' && auth()->user()->candidate) {
                    // Pencaker hanya dapat mengunduh CV mereka sendiri
                    $hasAccess = auth()->user()->candidate->id == $resume->candidate_id;
                }
            }

            // Jika tidak memiliki akses, redirect kembali
            if (!$hasAccess) {
                return redirect()->back()->with('error', 'Anda tidak memiliki akses untuk mengunduh CV ini');
            }

            $filePath = $resume->file;
            $filename = time().'.pdf';
            $headers = ['Content-Type: application/pdf', 'filename' => $filename];
            $fileName = rand().'-resume'.'.pdf';

            return response()->download($filePath, $fileName, $headers);
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Candidate preview cv
     *
     * @return void
     */
    public function candidatePreviewCv(CandidateResume $resume)
    {
        try {
            // Periksa apakah user login (baik sebagai user biasa atau admin)
            if (!auth()->check() && !auth('admin')->check()) {
                if (request()->ajax() || request()->wantsJson() || request()->expectsJson()) {
                    return response()->json(['error' => 'Anda harus login untuk melihat CV'], 401);
                }

                flashError('Anda harus login untuk melihat CV');
                return redirect()->route('login');
            }

            $hasAccess = false;

            // Periksa apakah user adalah admin
            if (auth('admin')->check()) {
                // Admin dapat melihat semua CV
                $hasAccess = true;
            } else if (auth()->check()) {
                // Periksa role user biasa
                if (auth()->user()->role == 'admin') {
                    // Admin dapat melihat semua CV
                    $hasAccess = true;
                } else if (auth()->user()->role == 'company' && auth()->user()->company) {
                    // Perusahaan hanya dapat melihat CV pencaker yang melamar ke lowongan mereka
                    $company_id = auth()->user()->company->id;
                    $hasAccess = DB::table('applied_jobs')
                        ->whereIn('job_id', function ($query) use ($company_id) {
                            $query->select('id')
                                ->from('jobs')
                                ->where('company_id', $company_id);
                        })
                        ->where('candidate_id', $resume->candidate_id)
                        ->exists();
                } else if (auth()->user()->role == 'candidate' && auth()->user()->candidate) {
                    // Pencaker hanya dapat melihat CV mereka sendiri
                    $hasAccess = auth()->user()->candidate->id == $resume->candidate_id;
                }
            }

            // Jika tidak memiliki akses, redirect kembali
            if (!$hasAccess) {
                if (request()->ajax() || request()->wantsJson() || request()->expectsJson()) {
                    return response()->json(['error' => 'Anda tidak memiliki akses untuk melihat CV ini'], 403);
                }

                return redirect()->back()->with('error', 'Anda tidak memiliki akses untuk melihat CV ini');
            }

            $filePath = $resume->file;

            // Pastikan file ada
            if (!file_exists($filePath)) {
                if (request()->ajax() || request()->wantsJson() || request()->expectsJson()) {
                    return response()->json(['error' => 'File tidak ditemukan'], 404);
                }

                flashError('File tidak ditemukan');
                return back();
            }

            // Baca file dan kembalikan sebagai response dengan header yang benar
            $content = file_get_contents($filePath);

            return response($content)
                ->header('Content-Type', 'application/pdf')
                ->header('Content-Disposition', 'inline; filename="cv-preview.pdf"');
        } catch (\Exception $e) {
            if (request()->ajax() || request()->wantsJson() || request()->expectsJson()) {
                return response()->json(['error' => $e->getMessage()], 500);
            }

            flashError('Terjadi kesalahan: '.$e->getMessage());
            return back();
        }
    }

    /**
     * Employer page
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function employees(Request $request)
    {
        try {
            abort_if(auth('user')->check() && authUser()->role == 'company', 404);

            $data = (new CompanyListService())->execute($request);

            // dd($data);

            return view('frontend.pages.employees', $data);
        } catch (\Exception $e) {
            flashError('An error occurred: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Employers details page
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function employersDetails(Request $request, $username)
    {
        try {
            $user = User::where('role', 'company')->where('username', $username)->first();

            if (!$user) {
                abort(404);
            }

            $data = (new CompanyDetailsService())->execute($user);

            // Handle AJAX request for jobs pagination
            if ($request->ajax()) {
                $perPage = $request->per_page ?? 3;
                $page = $request->page ?? 1;

                $jobs = $data['open_jobs']->paginate($perPage);

                $html = view('frontend.pages.partials.company-jobs', ['jobs' => $jobs])->render();
                $pagination = view('vendor.pagination.frontend', ['paginator' => $jobs])->render();

                return response()->json([
                    'html' => $html,
                    'pagination' => $pagination,
                    'total' => $jobs->total()
                ]);
            }

            // Set default pagination for initial load
            $data['open_jobs'] = $data['open_jobs']->paginate($request->per_page ?? 3);

            return view('frontend.pages.employe-details', $data);
        } catch (\Exception $e) {
            flashError('An error occurred: '.$e->getMessage());

            return back();
        }
    }

    /**
     * About page
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function about()
    {
        try {
            $testimonials = Testimonial::all();
            $companies = Company::count();
            $candidates = Candidate::count();

            return view('frontend.pages.about', compact('testimonials', 'companies', 'candidates'));
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Plan page
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function pricing()
    {
        try {
            abort_if(auth('user')->check() && authUser()->role == 'candidate', 404);
            $plans = Plan::active()->get();
            $plan_descriptions = $plans->pluck('descriptions')->flatten();

            $current_language = currentLanguage();
            $current_currency = currentCurrency();
            $current_language_code = $current_language ? $current_language->code : config('templatecookie.default_language');
            $faqs = Faq::where('code', currentLangCode())
                ->with('faq_category')
                ->whereHas('faq_category', function ($query) {
                    $query->where('name', 'Plan');
                })
                ->get();

            if ($current_language_code) {
                $plans->load([
                    'descriptions' => function ($q) use ($current_language_code) {
                        $q->where('locale', $current_language_code);
                    },
                ]);
            }

            return view('frontend.pages.pricing', compact('plans', 'faqs', 'current_language', 'plan_descriptions', 'current_currency', 'current_language_code'));
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Plan details page
     *
     * @param  string  $label
     * @return void
     */
    public function planDetails($label)
    {
        try {
            abort_if(! auth('user')->check(), 404);
            abort_if(auth('user')->check() && auth('user')->user()->role == 'candidate', 404);

            $data = (new PricePlanService())->details($label);

            return view('frontend.pages.plan-details', $data);
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Contact page
     *
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function contact()
    {
        return view('frontend.pages.contact');
    }

    /**
     * Faq page
     *
     * @param  Request  $request
     * @return \Illuminate\Contracts\Support\Renderable
     */
    public function faq()
    {
        try {
            $faq_categories = FaqCategory::with([
                'faqs' => function ($q) {
                    $q->where('code', currentLangCode());
                },
            ])->get();

            return view('frontend.pages.faq', compact('faq_categories'));
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    public function toggleBookmarkJob(Job $job)
    {
        try {
            // Pastikan user adalah pencaker dan memiliki profil candidate
            if (!auth('user')->check() || auth('user')->user()->role !== 'candidate') {
                if (request()->ajax() || request()->wantsJson() || request()->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Anda harus login sebagai pencaker untuk membookmark lowongan',
                        'bookmarked' => false
                    ], 403);
                }

                flashError('Anda harus login sebagai pencaker untuk membookmark lowongan');
                return back();
            }

            // Pastikan candidate ada
            if (!auth('user')->user()->candidate) {
                if (request()->ajax() || request()->wantsJson() || request()->expectsJson()) {
                    return response()->json([
                        'success' => false,
                        'message' => 'Profil pencaker tidak ditemukan',
                        'bookmarked' => false
                    ], 400);
                }

                flashError('Profil pencaker tidak ditemukan');
                return back();
            }

            // Toggle bookmark
            $check = $job->bookmarkJobs()->toggle(auth('user')->user()->candidate);

            // Periksa status bookmark setelah toggle
            $isBookmarked = !empty($check['attached']);

            if ($isBookmarked) {
                $user = auth('user')->user();
                // make notification to company candidate bookmark job
                Notification::send($job->company->user, new BookmarkJobNotification($user, $job));
            }

            $message = $isBookmarked ? __('job_added_to_favorite_list') : __('job_removed_from_favorite_list');

            // Check if request is AJAX
            if (request()->ajax() || request()->wantsJson() || request()->expectsJson()) {
                return response()->json([
                    'success' => true,
                    'message' => $message,
                    'bookmarked' => $isBookmarked
                ]);
            }

            flashSuccess($message);
            return back();
        } catch (\Exception $e) {
            if (request()->ajax() || request()->wantsJson() || request()->expectsJson()) {
                return response()->json([
                    'success' => false,
                    'message' => 'Terjadi kesalahan: ' . $e->getMessage(),
                    'bookmarked' => false
                ], 500);
            }

            flashError('Terjadi kesalahan: '.$e->getMessage());
            return back();
        }
    }

    public function toggleApplyJob(Request $request)
    {
        try {
            $validator = Validator::make(
                $request->all(),
                [
                    'resume_id' => 'required',
                    'cover_letter' => 'required',
                ],
                [
                    'resume_id.required' => 'Harap pilih CV/Resume',
                    'cover_letter.required' => 'Harap masukkan pesan',
                ],
            );

            if ($validator->fails()) {
                flashError($validator->errors()->first());

                return back();
            }

            // Cek kelengkapan profil pencaker
            $candidate = auth('user')->user()->candidate;
            $profileIncomplete = false;
            $missingFields = [];

            // Cek field wajib yang harus diisi
            if (!$candidate->user->name) {
                $missingFields[] = 'Nama lengkap';
                $profileIncomplete = true;
            }

            if (!$candidate->user->contactInfo || !$candidate->user->contactInfo->phone) {
                $missingFields[] = 'Nomor telepon';
                $profileIncomplete = true;
            }

            if (!$candidate->birth_date) {
                $missingFields[] = 'Tanggal lahir';
                $profileIncomplete = true;
            }

            if (!$candidate->gender) {
                $missingFields[] = 'Jenis kelamin';
                $profileIncomplete = true;
            }

            if (!$candidate->marital_status) {
                $missingFields[] = 'Status perkawinan';
                $profileIncomplete = true;
            }

            if (!$candidate->profession_id) {
                $missingFields[] = 'Profesi';
                $profileIncomplete = true;
            }

            // Cek apakah ada CV/Resume
            if ($candidate->resumes->count() == 0) {
                $missingFields[] = 'CV/Resume';
                $profileIncomplete = true;
            }

            if ($profileIncomplete) {
                $message = 'Lengkapi profil Anda sebelum melamar pekerjaan. Field yang belum diisi: ' . implode(', ', $missingFields);
                flashError($message);
                return redirect()->route('candidate.dashboard');
            }

            $candidate = auth('user')->user()->candidate;
            $job = Job::find($request->id);

            // Get default application group for 'Semua Lamaran'
            $default_group = \App\Models\DefaultApplicationGroup::where('name', 'Semua Lamaran')->first();

            // Get company application group for 'Semua Lamaran'
            $company_group = $job->company->applicationGroups->where('is_deleteable', false)->first();

            DB::table('applied_jobs')->insert([
                'candidate_id' => $candidate->id,
                'job_id' => $job->id,
                'cover_letter' => $request->cover_letter,
                'candidate_resume_id' => $request->resume_id,
                'application_group_id' => $company_group->id ?? 1,
                'default_application_group_id' => $default_group->id ?? null,
                'created_at' => now(),
                'updated_at' => now(),
            ]);

            // make notification to candidate and company for notify
            $job->company->user->notify(new ApplyJobNotification(auth('user')->user(), $job->company->user, $job));

            if (auth('user')->user()->recent_activities_alert) {
                auth('user')
                    ->user()
                    ->notify(new ApplyJobNotification(auth('user')->user(), $job->company->user, $job));
            }

            flashSuccess(__('job_applied_successfully'));

            return back();
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    public function register($role)
    {
        try {
            return view('frontend.auth.register', compact('role'));
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Get all posts
     *
     * @return void
     */
    public function posts(Request $request)
    {
        try {
            $code = currentLangCode();
            $key = request()->search;
            $posts = Post::query()
                ->where('locale', $code)
                ->published()
                ->withCount('comments');

            if ($key) {
                $posts->whereLike('title', $key);
            }

            if ($request->category) {
                $category_ids = PostCategory::whereIn('slug', $request->category)
                    ->get()
                    ->pluck('id');
                $posts = $posts
                    ->whereIn('category_id', $category_ids)
                    ->latest()
                    ->paginate(10)
                    ->withQueryString();
            } else {
                $posts = $posts
                    ->latest()
                    ->paginate(10)
                    ->withQueryString();
            }

            $recent_posts = Post::where('locale', $code)
                ->published()
                ->withCount('comments')
                ->latest()
                ->take(5)
                ->get();
            $categories = PostCategory::latest()->get();

            return view('frontend.pages.posts', compact('posts', 'categories', 'recent_posts'));
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Post details
     *
     * @param  string  $slug
     * @return void
     */
    public function post($slug)
    {
        try {
            $code = currentLangCode();
            $data['post'] = Post::published()
                ->whereSlug($slug)
                ->where('locale', $code)
                ->with(['author:id,name,name', 'comments.replies.user:id,name,image'])
                ->first();

            if (! $data['post']) {
                $current_language = getLanguageByCode($code);
                $post_language = getLanguageByCode(Post::whereSlug($slug)->value('locale'));
                $data['error_message'] = "This post is not available in {$current_language}, change the language to {$post_language} to see this post";

                flashError($data['error_message']);
                abort(404);
            }

            return view('frontend.pages.post', $data);
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Post comment
     *
     * @return void
     */
    public function comment(Post $post, Request $request)
    {
        try {
            if (! auth()->check()) {
                flashError(__('if_you_perform_this_action_you_need_to_login_your_account_first_do_you_want_to_login_now'));

                return redirect()->route('login');
            }

            $request->validate([
                'body' => 'required|max:2500|min:2',
            ]);

            $comment = new PostComment();
            $comment->author_id = auth()->user()->id;
            $comment->post_id = $post->id;
            if ($request->has('parent_id')) {
                $comment->parent_id = $request->parent_id;
                $redirect = '#replies-'.$request->parent_id;
            } else {
                $redirect = '#comments';
            }
            $comment->body = $request->body;
            $comment->save();

            return redirect(url()->previous().$redirect);
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Mark all notification as read
     *
     * @return void
     */
    public function markReadSingleNotification(Request $request)
    {
        try {
            $has_unread_notification = auth()
                ->user()
                ->unreadNotifications->count();

            if ($has_unread_notification && $request->id) {
                auth()
                    ->user()
                    ->unreadNotifications->where('id', $request->id)
                    ->markAsRead();
            }

            return true;
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Set session
     *
     * @return void
     */
    public function setSession(Request $request)
    {
        try {
            info($request->all());

            // Pastikan key 'lat' dan 'lng' selalu ada di session
            $locationData = $request->input();
            if (!isset($locationData['lat'])) {
                $locationData['lat'] = setting('default_lat');
            }
            if (!isset($locationData['lng'])) {
                $locationData['lng'] = setting('default_long');
            }

            $request->session()->put('location', $locationData);

            return response()->json(true);
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Set current location
     *
     * @param  Request  $request
     * @return void
     */
    public function setCurrentLocation($request)
    {
        return false;
        try {
            // Current Visitor Location Track && Set Country IF App Is Multi Country Base
            $app_country = setting('app_country_type');

            if ($app_country == 'multiple_base') {
                $ip = request()->ip();
                // $ip = '************'; // Bangladesh
                // $ip = '***************'; // Mauritius
                // $ip = '*************'; // AUD
                // $ip = '*************'; // SA
                // $ip = '************'; // United States"
                // $ip = '***********'; // Czech Republic
                // $ip = "************"; // Czechia
                // if ($ip) {
                //     $current_user_data = Location::get($ip);
                //     if ($current_user_data) {
                //         $user_country = $current_user_data->countryName;
                //         if ($user_country) {
                //             $this->setLangAndCurrency($user_country);
                //             $database_country = Country::where('name', $user_country)
                //                 ->where('status', 1)
                //                 ->first();
                //             if ($database_country) {
                //                 $selected_country = session()->get('selected_country');
                //                 if (! $selected_country) {
                //                     session()->put('selected_country', $database_country->id);

                //                     return true;
                //                 }
                //             }
                //         }
                //     }
                // } else {
                //     return false;
                // }
            } else {
                return false;
            }
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Process for set currency & language
     *
     * @param  string  $name
     * @return bool
     */
    public function setLangAndCurrency($name)
    {
        try {
            // this process for get language code/sort name  and currency sortname
            $get_lang_wise_sort_name = json_decode(file_get_contents(base_path('resources/backend/dummy-data/country_currency_language.json')), true);

            $country_name = Str::slug($name);
            if ($get_lang_wise_sort_name) {
                // loop json file data

                for ($i = 0; $i < count($get_lang_wise_sort_name); $i++) {
                    $json_country_name = Str::slug($get_lang_wise_sort_name[$i]['name']);

                    if ($country_name == $json_country_name) {
                        // check country are same

                        $cn_code = $get_lang_wise_sort_name[$i]['currency']['code'];
                        $ln_code = $get_lang_wise_sort_name[$i]['language']['code'];

                        // Currency setup
                        $set_currency = CurrencyModel::where('code', Str::upper($cn_code))->first();
                        if ($set_currency) {
                            session(['current_currency' => $set_currency]);
                            currencyRateStore();
                        }
                        // // Currency setup
                        $set_language = Language::where('code', Str::lower($ln_code))->first();
                        if ($set_language) {
                            session(['current_lang' => $set_language]);
                            // session()->put('set_lang', $lang);
                            app()->setLocale($ln_code);
                        }

                        // menu list cache clear
                        Cache::forget('menu_lists');

                        return true;
                    }
                }
            } else {
                return false;
            }
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Set selected country
     *
     * @return void
     */
    public function setSelectedCountry(Request $request)
    {
        try {
            session()->put('selected_country', $request->country);

            return back();
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Remove selected country
     *
     * @return void
     */
    public function removeSelectedCountry()
    {
        try {
            session()->forget('selected_country');

            return redirect()->back();
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Autocomplete di pencarian
     *
     * @return array
     */
    public function jobAutocomplete(Request $request)
    {
        try {
            $jobs = Job::select('title as value', 'id')
                ->where('title', 'LIKE', '%'.$request->get('search').'%')
                ->active()
                ->withoutEdited()
                ->latest()
                ->get()
                ->take(5);

            if ($jobs && count($jobs)) {
                $data = '<ul class="dropdown-menu show">';
                foreach ($jobs as $job) {
                    $jobValue = $job->value;
                    $data .= '<li class="dropdown-item"><a href="'.(auth()->check() ? route('website.job', ['keyword' => $jobValue]) : '#').'">'.$jobValue.'</a></li>';
                }
                $data .= '</ul>';
            } else {
                $data = '<ul class="dropdown-menu show"><li class="dropdown-item">Loker tidak ditemukan!</li></ul>';
            }

            return response()->json($data);
        } catch (\Exception $e) {
            flashError('Terjadi kesalahan: '.$e->getMessage());

            return back();
        }
    }

    /**
     * Get all districts for dropdown
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getLocality()
    {
        try {
            // Fetch all distinct districts from the jobs table and count the job listings for each district
            $districts = Job::select(
                    DB::raw("CONCAT(district, ', ', locality) as value"),
                    DB::raw('COUNT(*) as job_count')
                )
                ->where('status', 'active')
                ->whereNotNull('district')
                ->whereNotNull('locality')
                ->where('district', '!=', '')
                ->where('locality', '!=', '')
                ->groupBy('district', 'locality') // Group by both district and locality
                ->orderBy('district')
                ->orderBy('locality')
                ->get();

            // Check if districts were found and prepare the response
            if ($districts->isNotEmpty()) {
                $data = $districts->map(function ($district) {
                    return [
                        'value' => $district->value,
                        'job_count' => $district->job_count, // Include job count
                    ];
                });
                return response()->json($data); // Return the array of districts as JSON
            } else {
                return response()->json([]); // Return empty array instead of error
            }
        } catch (\Exception $e) {
            \Log::error('Error getting localities: ' . $e->getMessage());
            return response()->json(['error' => 'Terjadi kesalahan.'], 500); // Return error response
        }
    }

    /**
     * Get all cities with job counts
     *
     * @return \Illuminate\Http\JsonResponse
     */
    public function getCitiesWithJobs()
    {
        try {
            // Get cities from jobs table
            $cities = Job::select('district as name', DB::raw('COUNT(*) as job_count'))
                ->where('status', 'active')
                ->whereNotNull('district')
                ->where('district', '!=', '')
                ->groupBy('district')
                ->orderBy('district')
                ->get();

            // Log for debugging
            \Log::info('Cities with jobs query result: ' . $cities->count());

            if ($cities->isEmpty()) {
                // Fallback to city model if no cities found in jobs
                $cities = City::select('name', DB::raw('0 as job_count'))
                    ->orderBy('name')
                    ->limit(20) // Limit to prevent too many results
                    ->get();

                \Log::info('Fallback to city model: ' . $cities->count());
            }

            return response()->json($cities);
        } catch (\Exception $e) {
            \Log::error('Error getting cities with jobs: ' . $e->getMessage());
            return response()->json(['error' => 'Terjadi kesalahan.'], 500);
        }
    }

    /**
     * Get kecamatan by city name
     *
     * @param Request $request
     * @return \Illuminate\Http\JsonResponse
     */
    public function getKecamatanByCity(Request $request)
    {
        try {
            $cityName = $request->city;

            if (!$cityName) {
                return response()->json([]);
            }

            // Get kecamatan from jobs table based on city
            $kecamatan = Job::select('locality as value', DB::raw('COUNT(*) as job_count'))
                ->where('status', 'active')
                ->where('district', $cityName)
                ->whereNotNull('locality')
                ->where('locality', '!=', '')
                ->groupBy('locality')
                ->orderBy('locality')
                ->get();

            return response()->json($kecamatan);
        } catch (\Exception $e) {
            return response()->json(['error' => 'Terjadi kesalahan.'], 500);
        }
    }


    public function successTransaction(Request $request)
    {
        $provider = new PayPal;
        $provider->setApiCredentials(config('paypal'));
        $provider->getAccessToken();
        $response = $provider->capturePaymentOrder($request['token']);

        if (isset($response['status']) && $response['status'] == 'COMPLETED') {
            session(['transaction_id' => $response['id'] ?? null]);

            $this->orderPlacing();
        } else {
            session()->flash('error', __('payment_was_failed'));

            return back();
        }
    }

    public function notifDemo()
    {
        // Set notifikasi dengan session flash
        session()->flash('notification', 'Pesan notifikasi untuk user!');

        // Redirect ke halaman yang diinginkan
        return redirect()->route('home');
    }

    /**
     * Get related candidates based on age and education similarity
     *
     * @param User $candidate
     * @param Request $request
     * @return \Illuminate\Contracts\Pagination\LengthAwarePaginator
     */
    private function getRelatedCandidates($candidate, $request = null)
    {
        $query = User::where('role', 'candidate')
            ->where('id', '!=', $candidate->id)
            ->whereHas('candidate', function ($q) {
                $q->where('visibility', 1);
            })
            ->with(['candidate.profession', 'candidate.education']);

        // Filter by similar age (±1 year)
        if ($candidate->candidate && $candidate->candidate->birth_date) {
            $candidateAge = \Carbon\Carbon::parse($candidate->candidate->birth_date)->age;
            $minAge = $candidateAge - 1;
            $maxAge = $candidateAge + 1;

            $query->whereHas('candidate', function ($q) use ($minAge, $maxAge) {
                $q->whereRaw('TIMESTAMPDIFF(YEAR, birth_date, CURDATE()) BETWEEN ? AND ?', [$minAge, $maxAge]);
            });
        }

        // Filter by same education level
        if ($candidate->candidate && $candidate->candidate->education_id) {
            $query->whereHas('candidate', function ($q) use ($candidate) {
                $q->where('education_id', $candidate->candidate->education_id);
            });
        }

        // Get per_page from request, default to 3
        $perPage = $request && $request->has('per_page') ? $request->per_page : 3;

        // Always return paginated results to ensure total() method exists
        $result = $query->paginate($perPage);

        // If no related candidates found, return empty paginated result
        if ($result->isEmpty()) {
            // Create empty paginated result with proper structure
            return new \Illuminate\Pagination\LengthAwarePaginator(
                collect([]), // empty collection
                0, // total items
                $perPage, // per page
                1, // current page
                [
                    'path' => request()->url(),
                    'pageName' => 'page',
                ]
            );
        }

        return $result;
    }

}
