@foreach($relatedCandidates as $relatedCandidate)
    @if($relatedCandidate->candidate)
    <div class="col-md-6 col-lg-4 rt-mb-24">
        <div class="card jobcardStyle1 body-24">
            <div class="card-body">
                <a href="{{ route('website.candidate.profile', $relatedCandidate->username) }}" class="text-decoration-none">
                    <div class="rt-single-icon-box icb-clmn-lg tw-relative">
                        <div class="icon-thumb tw-relative">
                            <div class="profile-image">
                                <img src="{{ $relatedCandidate->candidate->photo }}" alt="Foto {{ $relatedCandidate->username }}">
                            </div>
                            <div class="tw-absolute tw-top-0 tw-left-1">
                                @if ($relatedCandidate->candidate->status == 'available')
                                    <div class="tw-inline-flex">
                                        <svg width="14" height="14" viewBox="0 0 14 14"
                                            fill="none" xmlns="http://www.w3.org/2000/svg">
                                            <circle cx="7" cy="7" r="6"
                                                fill="#2ecc71" stroke="white" stroke-width="2">
                                            </circle>
                                        </svg>
                                    </div>
                                @endif
                            </div>
                        </div>
                        <div class="iconbox-content !tw-m-0">
                            <div class="job-mini-title">
                                <span>{{ $relatedCandidate->name }}</span>
                            </div>
                            @if($relatedCandidate->candidate->birth_date)
                                <span class="loacton text-gray-500">
                                    {{ \Carbon\Carbon::parse($relatedCandidate->candidate->birth_date)->age }} Tahun
                                </span>
                            @endif
                            <span class="body-font-4 mt-1 text-gray-900 d-block">
                                {{ $relatedCandidate->candidate->education ? $relatedCandidate->candidate->education->name : 'Pendidikan tidak diketahui' }}
                            </span>
                        </div>
                    </div>
                </a>
                <div class="mt-3">
                    <button onclick="showCandidateProfileModal('{{ $relatedCandidate->username }}')"
                        class="btn btn-outline-primary btn-sm w-100">
                        {{ __('view_resume') }}
                    </button>
                </div>
            </div>
        </div>
    </div>
    @endif
@endforeach
