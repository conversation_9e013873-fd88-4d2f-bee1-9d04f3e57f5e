@if ($bookmarks->count() > 0)
    <div class="tw-grid tw-grid-cols-1 md:tw-grid-cols-2 lg:tw-grid-cols-3 tw-gap-4">
        @foreach ($bookmarks as $candidate)
            <div class="candidate-card tw-bg-white tw-rounded-lg tw-shadow-sm tw-overflow-hidden tw-transition-all tw-duration-300 hover:tw-shadow-md">
                <div class="tw-p-4">
                    <div class="tw-flex tw-items-start tw-gap-4">
                        <div class="candidate-avatar tw-relative">
                            <a href="javascript:void(0)" onclick="showCandidateProfileModal('{{ $candidate->user->username }}')" class="tw-block">
                                <img src="{{ asset($candidate->photo) }}" alt="Foto {{ $candidate->user->name }}" 
                                    class="tw-w-16 tw-h-16 tw-rounded-full tw-object-cover tw-border tw-border-gray-200">
                            </a>
                            @if ($candidate->status == 'available')
                                <span class="tw-absolute tw-bottom-0 tw-right-0 tw-w-4 tw-h-4 tw-bg-green-500 tw-rounded-full tw-border-2 tw-border-white"></span>
                            @endif
                        </div>
                        <div class="candidate-info tw-flex-1">
                            <h4 class="tw-text-lg tw-font-semibold tw-mb-1">
                                <a href="javascript:void(0)" onclick="showCandidateProfileModal('{{ $candidate->user->username }}')" class="tw-text-gray-900 hover:tw-text-primary-500">
                                    {{ $candidate->user->name }}
                                </a>
                            </h4>
                            <p class="tw-text-gray-500 tw-mb-1">
                                {{ $candidate->education ? $candidate->education->name : 'Pendidikan tidak diketahui' }}
                            </p>
                            @php
                                $domisili = $candidate->district ?? $candidate->user->kabupaten_kota ?? '';
                                $domisili = ucwords(strtolower($domisili));
                            @endphp
                            <p class="tw-text-gray-600 tw-mb-1 tw-text-sm">
                                {{ $domisili }}
                            </p>
                            @if ($candidate->birth_date)
                                <p class="tw-text-gray-500 tw-mb-2 tw-text-sm">
                                    {{ \Carbon\Carbon::parse($candidate->birth_date)->age }} Tahun
                                </p>
                            @endif
                            <div class="candidate-actions tw-flex tw-items-center tw-gap-2 tw-mt-3">
                                <form action="{{ route('company.companybookmarkcandidate', $candidate->id) }}" method="POST" class="tw-inline-block">
                                    @csrf
                                    <button type="submit" class="tw-bg-gray-100 hover:tw-bg-gray-200 tw-text-gray-700 tw-p-2 tw-rounded-full tw-transition-colors">
                                        <x-svg.bookmark2-icon />
                                    </button>
                                </form>
                                
                                @if (auth()->user()->status)
                                    <a onclick="showCandidateProfileModal('{{ $candidate->user->username }}')" 
                                       class="btn btn-primary tw-rounded-md tw-py-2 tw-px-3 tw-flex tw-items-center tw-gap-1" 
                                       href="javascript:void(0)">
                                        <span>{{ __('view_profile') }}</span>
                                        <i class="ph-arrow-right"></i>
                                    </a>
                                @else
                                    <a onclick="toastr.warning('{{ __('your_account_is_not_active_please_wait_until_the_account_is_activated_by_admin') }}')" 
                                       class="btn btn-primary tw-rounded-md tw-py-2 tw-px-3 tw-flex tw-items-center tw-gap-1" 
                                       href="javascript:void(0)">
                                        <span>{{ __('view_profile') }}</span>
                                        <i class="ph-arrow-right"></i>
                                    </a>
                                @endif
                                
                                <div class="tw-ml-auto">
                                    <div class="dropdown">
                                        <button type="button" class="tw-bg-gray-100 hover:tw-bg-gray-200 tw-text-gray-700 tw-p-2 tw-rounded-full tw-transition-colors" 
                                                id="dropdownMenuButton{{ $candidate->id }}" 
                                                data-bs-toggle="dropdown" 
                                                aria-expanded="false">
                                            <i class="ph-dots-three-vertical"></i>
                                        </button>
                                        <ul class="dropdown-menu dropdown-menu-end" aria-labelledby="dropdownMenuButton{{ $candidate->id }}">
                                            <li>
                                                <a target="__blank" class="dropdown-item tw-flex tw-items-center tw-gap-2" href="mailto:{{ $candidate->user->email }}">
                                                    <i class="ph-envelope"></i>
                                                    {{ __('send_email') }}
                                                </a>
                                            </li>
                                            @if ($candidate->user->contactInfo && $candidate->user->contactInfo->phone)
                                                <li>
                                                    <a target="__blank" class="dropdown-item tw-flex tw-items-center tw-gap-2" href="https://wa.me/{{ $candidate->user->contactInfo->phone }}">
                                                        <i class="ph-whatsapp-logo"></i>
                                                        {{ __('Kirim WhatsApp') }}
                                                    </a>
                                                </li>
                                            @endif
                                        </ul>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endforeach
    </div>
@else
    <div class="tw-bg-white tw-rounded-lg tw-shadow-sm tw-p-8 tw-text-center">
        <div class="tw-flex tw-justify-center tw-mb-4">
            <x-svg.not-found-icon width="120" height="120" />
        </div>
        <h3 class="tw-text-xl tw-font-medium tw-text-gray-700 tw-mb-2">{{ __('no_data_found') }}</h3>
        <p class="tw-text-gray-500">Anda belum memiliki kandidat yang disimpan dalam bookmark.</p>
    </div>
@endif
