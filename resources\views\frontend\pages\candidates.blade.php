@extends('frontend.layouts.app')

@section('description')
    @php
        $data = metaData('candidates');
    @endphp
    {{ $data->description }}
@endsection
@section('og:image')
    {{ asset($data->image) }}
@endsection
@section('title')
    {{ $data->title }}
@endsection

@section('main')
    <form action="{{ route('website.candidate') }}" method="GET" id="candidate_search_form" autocomplete="off">
        <div class="breadcrumbs style-two">
            <div class="container">
                <div class="row align-items-center ">
                    <div class="col-12 position-relative ">
                        <div class="breadcrumb-menu">
                            <h6 class="f-size-18 m-0">{{ __('find_candidates') }}</h6>
                            <ul>
                                <li><a href="{{ route('website.home') }}">{{ __('home') }}</a></li>
                                <li>/</li>
                                <li>{{ __('candidates') }}</li>
                            </ul>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Mobile Filter Button -->
        <button type="button" class="mobile-filter-btn" id="mobile-filter-btn">
            <i class="ph-funnel"></i>
        </button>

        <!-- Mobile Filter Sidebar -->
        <div class="filter-sidebar" id="filter-sidebar">
            <div class="tw-flex tw-justify-between tw-items-center tw-mb-4">
                <h5 class="tw-text-lg tw-font-medium tw-mb-0">{{ __('filter') }}</h5>
                <button type="button" class="tw-text-gray-500" id="close-filter-sidebar">
                    <i class="ph-x tw-text-xl"></i>
                </button>
            </div>

            <!-- Mobile Filter Content - Will be populated with JS -->
            <div id="mobile-filter-content"></div>

            <div class="tw-mt-4">
                <button type="button" id="mobile-apply-filter" class="btn btn-primary tw-w-full">{{ __('apply_filter') }}</button>
            </div>
        </div>

        <div class="filter-overlay" id="filter-overlay"></div>
        <div class="candidate-filter-overlay"></div>

        <div class="candidatelist-content">
            <div class="container">
                <div class="row mt-5">
                    <!-- Left Sidebar - Filters (25%) -->
                    <div class="col-lg-3">
                        <div class="tw-bg-white tw-rounded-lg tw-shadow-sm tw-p-4 tw-mb-4 sticky-filter">
                            <div class="tw-flex tw-justify-between tw-items-center tw-mb-4">
                                <h5 class="tw-text-lg tw-font-medium tw-mb-0">{{ __('filter') }}</h5>
                                @if (request('keyword') || request('profession') || request('experience') || request('education') || request('gender') || request('umur_min') || request('umur_max'))
                                <button type="button" id="reset-filters" class="btn btn-sm btn-outline-secondary">
                                    <i class="ph-arrow-counter-clockwise tw-mr-1"></i> {{ __('Reset') }}
                                </button>
                                @endif
                            </div>

                            <!-- Search Keyword -->
                            <div class="tw-mb-4">
                                <label for="Cari Pencaker" class="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-1">{{ __('Kata Kunci') }}</label>
                                <div class="tw-relative">
                                    <input id="search_candidates" name="keyword" type="text" placeholder="{{ __('Cari Kandidat') }}"
                                        value="{{ request('keyword') }}" autocomplete="off" class="form-control tw-pl-10">
                                    <div class="tw-absolute tw-top-1/2 -tw-translate-y-1/2 tw-left-3">
                                        <x-svg.search-icon />
                                    </div>
                                </div>
                            </div>

                            <!-- Keahlian -->
                            <div class="tw-mb-4">
                                <label for="keahlian_select" class="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-1">{{ __('Keahlian') }}</label>
                                <select name="keahlian" class="form-control" id="keahlian_select">
                                    <option value="">{{ __('Pilih Keahlian') }}</option>
                                    @foreach ($keahlian as $skill)
                                        <option value="{{ $skill }}" {{ $skill == request('keahlian') ? 'selected' : '' }}>
                                            {{ $skill }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Experience -->
                            <div class="tw-mb-4">
                                <label for="experience_select" class="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-1">{{ __('experience') }}</label>
                                <select name="experience" class="form-control" id="experience_select">
                                    <option value="">{{ __('Pilih Pengalaman') }}</option>
                                    @foreach ($experiences as $experience)
                                        <option value="{{ $experience->name }}" {{ $experience->name == request('experience') ? 'selected' : '' }}>
                                            {{ $experience->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Education -->
                            <div class="tw-mb-4">
                                <label for="education_select" class="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-1">{{ __('education') }}</label>
                                <select name="education" class="form-control" id="education_select">
                                    <option value="">{{ __('Pilih Pendidikan') }}</option>
                                    @foreach ($educations as $education)
                                        <option value="{{ $education->name }}" {{ $education->name == request('education') ? 'selected' : '' }}>
                                            {{ $education->name }}
                                        </option>
                                    @endforeach
                                </select>
                            </div>

                            <!-- Gender Filter -->
                            <div class="tw-mb-4">
                                <label for="gender-select" class="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-1">{{ __('Jenis Kelamin') }}</label>
                                <select name="gender" id="gender-select" class="form-control">
                                    <option value="" {{ !request('gender') ? 'selected' : '' }}>Semua</option>
                                    <option value="male" {{ request('gender') == 'male' ? 'selected' : '' }}>Pria</option>
                                    <option value="female" {{ request('gender') == 'female' ? 'selected' : '' }}>Wanita</option>
                                </select>
                            </div>

                            <!-- Age Range -->
                            <div class="tw-mb-4">
                                <label class="tw-block tw-text-sm tw-font-medium tw-text-gray-700 tw-mb-2">{{ __('Rentang Umur') }}</label>
                                <div class="tw-flex tw-gap-2 tw-items-center">
                                    <div class="tw-flex-1">
                                        <input type="number" name="umur_min" id="umur_min" class="form-control tw-text-center"
                                            value="{{ request('umur_min') }}" placeholder="18" min="17" max="65" style="max-width: 70px;">
                                    </div>
                                    <span class="tw-text-gray-500 tw-text-sm">-</span>
                                    <div class="tw-flex-1">
                                        <input type="number" name="umur_max" id="umur_max" class="form-control tw-text-center"
                                            value="{{ request('umur_max') }}" placeholder="65" min="17" max="65" style="max-width: 70px;">
                                    </div>
                                    <span class="tw-text-xs tw-text-gray-500 tw-whitespace-nowrap">tahun</span>
                                </div>
                            </div>



                            <!-- Apply Filter Button -->
                            <div class="tw-mt-6">
                                <button type="button" id="apply-filter-btn" class="btn btn-primary tw-w-full">{{ __('apply_filter') }}</button>
                                <div id="filter-loading" class="tw-text-center tw-mt-2 tw-hidden">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status">
                                        <span class="visually-hidden">Loading...</span>
                                    </div>
                                    <span class="tw-text-sm tw-text-gray-600 tw-ml-2">Memuat...</span>
                                </div>
                            </div>
                        </div>

                        <!-- Active Filters Display -->
                        @if (request('keyword') ||
                                request('keahlian') ||
                                request('experience') ||
                                request('education') ||
                                request('gender') ||
                                request('umur_min') ||
                                request('umur_max'))
                            <div class="tw-bg-white tw-rounded-lg tw-shadow-sm tw-p-4 tw-mb-4">
                                <h6 class="tw-text-sm tw-font-medium tw-mb-2">{{ __('active_filter') }}</h6>
                                <div class="tw-flex tw-flex-col tw-gap-2">
                                    @if (request('keyword'))
                                        <div class="tw-flex tw-justify-between tw-items-center">
                                            <span class="tw-text-sm">{{ __('search') }}: {{ request('keyword') }}</span>
                                            <button type="button" class="tw-text-gray-400 hover:tw-text-red-500" onclick="keywordClose()">
                                                <x-svg.tw-close-icon />
                                            </button>
                                        </div>
                                    @endif
                                    @if (request('keahlian'))
                                        <div class="tw-flex tw-justify-between tw-items-center">
                                            <span class="tw-text-sm">{{ __('Keahlian') }}: {{ request('keahlian') }}</span>
                                            <button type="button" class="tw-text-gray-400 hover:tw-text-red-500" onclick="keahlianClose()">
                                                <x-svg.tw-close-icon />
                                            </button>
                                        </div>
                                    @endif
                                    @if (request('experience'))
                                        <div class="tw-flex tw-justify-between tw-items-center">
                                            <span class="tw-text-sm">{{ __('experience') }}: {{ request('experience') }}</span>
                                            <button type="button" class="tw-text-gray-400 hover:tw-text-red-500" onclick="experienceClose()">
                                                <x-svg.tw-close-icon />
                                            </button>
                                        </div>
                                    @endif
                                    @if (request('education'))
                                        <div class="tw-flex tw-justify-between tw-items-center">
                                            <span class="tw-text-sm">{{ __('education') }}: {{ request('education') }}</span>
                                            <button type="button" class="tw-text-gray-400 hover:tw-text-red-500" onclick="educationClose()">
                                                <x-svg.tw-close-icon />
                                            </button>
                                        </div>
                                    @endif
                                    @if (request('gender'))
                                        <div class="tw-flex tw-justify-between tw-items-center">
                                            <span class="tw-text-sm">Jenis Kelamin: {{ request('gender') == 'male' ? 'Pria' : 'Wanita' }}</span>
                                            <button type="button" class="tw-text-gray-400 hover:tw-text-red-500" onclick="genderClose()">
                                                <x-svg.tw-close-icon />
                                            </button>
                                        </div>
                                    @endif
                                    @if (request('umur_min') || request('umur_max'))
                                        <div class="tw-flex tw-justify-between tw-items-center">
                                            <span class="tw-text-sm">Umur: {{ request('umur_min', '17') }} - {{ request('umur_max', '65') }} tahun</span>
                                            <button type="button" class="tw-text-gray-400 hover:tw-text-red-500" onclick="ageClose()">
                                                <x-svg.tw-close-icon />
                                            </button>
                                        </div>
                                    @endif

                                </div>
                            </div>
                        @endif
                    </div>

                    <!-- Right Content - Candidate Listings (75%) -->
                    <div class="col-lg-9">
                        <!-- Loading Overlay -->
                        <div id="candidates-loading" class="tw-hidden tw-text-center tw-py-8">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                            <p class="tw-mt-2 tw-text-gray-600">Memuat kandidat...</p>
                        </div>

                        <!-- Candidates Content -->
                        <div id="candidates-content">
                            @include('frontend.pages.partials.candidates-content')
                        </div> <!-- End candidates-content -->
                    </div>
                </div>
            </div>
        </div>
    </form>

    <div class="rt-spacer-100 rt-spacer-md-50"></div>

    {{-- Subscribe Newsletter --}}
    <x-website.subscribe-newsletter />

    <!-- ===================================== -->
    <div class="modal fade cadidate-modal" id="aemploye-profile" tabindex="-1" aria-labelledby="exampleModalLabel"
        aria-modal="true" role="dialog">
        <div class="modal-dialog modal-wrapper modal-lg">
            <div class="modal-content">
                <div class="modal-body">
                    <h5 class="text-center">
                        {{ __('save_to') }}
                    </h5>
                    <div class="row border-top">
                        <div class="col-md-12" id="categoryList">
                        </div>
                        <div class="col-md-12 tw-mt-3">
                            <div class="saved-candidate">
                                <a class="btn btn-primary" target="_blank" href="{{ route('company.bookmark.category.index') }}">
                                    <span>{{ __('create_category') }}</span>
                                </a>
                            </div>
                        </div>
                    </div>
                </div>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
        </div>
    </div>

    <!-- Modal -->
    <x-website.modal.candidate-profile-modal />



     <!-- Find Candidate Id From Here | Please don't remove it Start -->
    <input type="hidden" value="" id="candidate_id">
    <!-- Find Candidate Id From Here | Please don't remove it End -->

@endsection

@push('frontend_links')
<link rel="stylesheet" href="{{ asset('backend') }}/plugins/select2/css/select2.min.css">
    <link rel="stylesheet" href="{{ asset('backend') }}/plugins/select2-bootstrap4-theme/select2-bootstrap4.min.css">
    <style>
        /* Mobile Filter Styles */
        .mobile-filter-btn {
            position: fixed;
            bottom: 20px;
            left: 20px;
            z-index: 1000;
            background: var(--primary-500, #0A65CC);
            color: white;
            border: none;
            border-radius: 50%;
            width: 56px;
            height: 56px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.15);
            display: none;
        }

        .filter-sidebar {
            position: fixed;
            top: 0;
            left: -100%;
            width: 300px;
            height: 100vh;
            background: white;
            z-index: 1050;
            transition: left 0.3s ease;
            padding: 20px;
            overflow-y: auto;
        }

        .filter-sidebar.active {
            left: 0;
        }

        .filter-overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background: rgba(0,0,0,0.5);
            z-index: 1040;
            display: none;
        }

        .filter-overlay.active {
            display: block;
        }

        /* Sticky Filter */
        .sticky-filter {
            position: sticky;
            top: 20px;
            max-height: calc(100vh - 40px);
            overflow-y: auto;
        }

        /* Select2 Custom Styling */
        .select2-container {
            width: 100% !important;
        }

        .select2-container--bootstrap4 .select2-selection--multiple {
            min-height: 38px;
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
        }

        .select2-container--bootstrap4 .select2-selection--multiple .select2-selection__choice {
            background-color: #0A65CC;
            border-color: #0A65CC;
            color: white;
            font-size: 12px;
            margin: 2px;
            padding: 2px 8px;
        }

        .select2-container--bootstrap4 .select2-selection--multiple .select2-selection__choice__remove {
            color: white;
            margin-right: 5px;
        }

        .select2-container--bootstrap4 .select2-selection--multiple .select2-selection__choice__remove:hover {
            color: #fff;
            background-color: rgba(255,255,255,0.2);
        }

        .select2-dropdown {
            border: 1px solid #ced4da;
            border-radius: 0.375rem;
        }

        .select2-container--bootstrap4.select2-container--focus .select2-selection {
            border-color: #80bdff;
            box-shadow: 0 0 0 0.2rem rgba(0, 123, 255, 0.25);
        }

        /* Loading Animation */
        #filter-loading {
            transition: all 0.3s ease;
        }

        /* Candidate Grid Loading */
        .candidate-loading {
            opacity: 0.6;
            pointer-events: none;
            transition: opacity 0.3s ease;
        }

        @media (max-width: 991.98px) {
            .mobile-filter-btn {
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .col-lg-3 {
                display: none;
            }

            .col-lg-9 {
                flex: 0 0 100%;
                max-width: 100%;
            }

            .sticky-filter {
                position: relative;
                top: auto;
                max-height: none;
            }
        }
        @media(min-width: 768px) {
            #aemploye-profile .modal-wrapper {
                width: 30% !important;
                margin: 1.75rem auto !important;
            }
        }

        #aemploye-profile .modal-body {
            overflow-x: hidden !important;
            overflow-y: auto !important;
        }

        .benefits-tags {
            display: flex;
            flex-wrap: wrap;
            gap: 12px;
        }

        .benefits-tags input,
        .technologies input {
            position: absolute;
            opacity: 0;
        }

        .benefits-tags span {
            font-weight: 400;
            font-size: 14px;
            line-height: 20px;
            color: #474C54;
            background: rgba(241, 242, 244, 0.4);
            border: 1px solid #E4E5E8;
            border-radius: 4px;
            padding: 8px 12px;
            cursor: pointer;
        }

        .benefits-tags input:checked~span,
        .benefits-tags span.active {
            color: #0A65CC;
            background: #E7F0FA;
            border: 1px solid #0A65CC;
            border-radius: 4px;
        }

        .mr--10px {
            margin-right: 10px
        }

        .ml--10px {
            margin-left: 10px
        }

        .tooltip-inner {
            max-width: 300px;
        }

        .whatsapp-button {
            background: rgb(243, 243, 243);
            border-radius: 35px;
            display: flex;
            -webkit-box-flex: 2;
            flex-grow: 2;
            -webkit-box-pack: center;
            justify-content: center;
            -webkit-box-align: center;
            align-items: center;
            height: 40px;
            border: 1px solid rgb(223, 223, 223);
            width: 144px;
            max-width: 160px;
            margin-top: 10px;
            margin-bottom: 10px;
            font-size: 15px;
        }

        /* Skeleton loading animation */
        @keyframes pulse {
            0% {
                opacity: 0.6;
                transform: scale(1);
            }
            50% {
                opacity: 1;
                transform: scale(1.01);
            }
            100% {
                opacity: 0.6;
                transform: scale(1);
            }
        }

        @keyframes shimmer {
            0% {
                background-position: -468px 0;
            }
            100% {
                background-position: 468px 0;
            }
        }

        .tw-animate-pulse {
            animation: pulse 2s infinite ease-in-out;
        }

        .tw-animate-pulse .tw-bg-gray-200 {
            background: linear-gradient(to right, #f0f0f0 8%, #e0e0e0 18%, #f0f0f0 33%);
            background-size: 800px 104px;
            animation: shimmer 1.5s infinite linear;
        }

        .skeleton-card {
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .skeleton-card:hover {
            box-shadow: 0px 12px 30px rgba(0, 0, 0, 0.1);
            transform: translateY(-2px);
        }

        /* Pagination styling */
        .pagination {
            display: flex;
            justify-content: center;
            list-style: none;
            padding: 0;
            margin: 20px 0;
        }

        .page-item {
            margin: 0 3px;
        }

        .page-link {
            display: flex;
            align-items: center;
            justify-content: center;
            height: 40px;
            width: 40px;
            border-radius: 50%;
            color: #767F8C;
            background-color: #fff;
            border: 1px solid #E4E5E8;
            text-decoration: none;
            transition: all 0.3s ease;
        }

        .page-link:hover {
            background-color: #E7F0FA;
            color: #0A65CC;
            border-color: #0A65CC;
        }

        .page-item.active .page-link {
            background-color: #0A65CC;
            color: #fff;
            border-color: #0A65CC;
        }

        .page-item.disabled .page-link {
            color: #C8CCD1;
            pointer-events: none;
            background-color: #fff;
            border-color: #E4E5E8;
        }

        /* Grid view card styling */
        #nav-home .card {
            height: 100%;
            transition: transform 0.2s ease, box-shadow 0.2s ease;
        }

        #nav-home .card:hover {
            transform: translateY(-2px);
            box-shadow: 0 4px 12px rgba(0,0,0,0.1);
        }

        #nav-home .card-body {
            display: flex;
            flex-direction: column;
            height: 100%;
        }

        #nav-home .card-body > div:last-child {
            margin-top: auto;
        }
    </style>
@endpush

@section('script')
    <script src="{{ asset('backend') }}/plugins/select2/js/select2.full.min.js"></script>
    <script>
        $(document).ready(function() {

            // Mobile filter functionality
            $('#mobile-filter-btn').click(function() {
                $('#filter-sidebar').addClass('active');
                $('#filter-overlay').addClass('active');
                $('body').css('overflow', 'hidden');
            });

            $('#close-filter-sidebar, #filter-overlay').click(function() {
                $('#filter-sidebar').removeClass('active');
                $('#filter-overlay').removeClass('active');
                $('body').css('overflow', 'auto');
            });

            // Apply filter button with AJAX
            $('#apply-filter-btn, #mobile-apply-filter').click(function() {
                applyFiltersWithAjax();
            });

            // Handle candidate login required
            $(document).on('click', '.candidate_login_required', function(e) {
                e.preventDefault();

                Swal.fire({
                    title: 'Login Sebagai Perusahaan Diperlukan',
                    text: 'Silakan login sebagai perusahaan untuk melihat profil kandidat',
                    icon: 'info',
                    showCancelButton: true,
                    confirmButtonText: 'Login Sekarang',
                    cancelButtonText: 'Batal'
                }).then((result) => {
                    if (result.isConfirmed) {
                        window.location.href = '{{ route("login") }}';
                    }
                });
            });

            // Handle view resume button clicks
            $(document).on('click', '[onclick*="showCandidateProfileModal"]', function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Extract username from onclick attribute
                const onclickAttr = $(this).attr('onclick');
                const usernameMatch = onclickAttr.match(/showCandidateProfileModal\('([^']+)'\)/);

                if (usernameMatch && usernameMatch[1]) {
                    showCandidateProfileModal(usernameMatch[1]);
                }
            });

            // Reset filters with AJAX
            $('#reset-filters').click(function() {
                // Clear all form inputs
                $('#candidate_search_form')[0].reset();
                // Apply filters with AJAX
                applyFiltersWithAjax();
            });

            // Auto-submit on select change with AJAX
            $('#keahlian_select, #experience_select, #education_select, #gender-select').change(function() {
                applyFiltersWithAjax();
            });

            // Auto-submit on input change for age fields
            $('#umur_min, #umur_max').on('input', debounce(function() {
                applyFiltersWithAjax();
            }, 500));

            // Auto-submit on keyword input
            $('#search_candidates').on('input', debounce(function() {
                applyFiltersWithAjax();
            }, 500));



            // Copy desktop filters to mobile sidebar
            function copyFiltersToMobile() {
                const desktopFilters = $('.col-lg-3 .sticky-filter').clone();
                // Remove the apply filter button from cloned content to avoid duplication
                desktopFilters.find('#apply-filter-btn, .tw-mt-6').remove();
                $('#mobile-filter-content').html(desktopFilters);
            }

            copyFiltersToMobile();
        });



        // Debounce function
        function debounce(func, wait) {
            let timeout;
            return function executedFunction(...args) {
                const later = () => {
                    clearTimeout(timeout);
                    func(...args);
                };
                clearTimeout(timeout);
                timeout = setTimeout(later, wait);
            };
        }

        // Apply filters with AJAX
        function applyFiltersWithAjax() {
            // Show loading
            $('#candidates-loading').removeClass('tw-hidden');
            $('#candidates-content').addClass('candidate-loading');
            $('#filter-loading').removeClass('tw-hidden');

            // Get form data
            const formData = $('#candidate_search_form').serialize();
            const url = $('#candidate_search_form').attr('action');

            $.ajax({
                url: url,
                type: 'GET',
                data: formData,
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(response) {
                    // Update candidates content
                    $('#candidates-content').html(response);

                    // Hide loading
                    $('#candidates-loading').addClass('tw-hidden');
                    $('#candidates-content').removeClass('candidate-loading');
                    $('#filter-loading').addClass('tw-hidden');

                    // Update URL without page reload
                    const newUrl = url + '?' + formData;
                    window.history.pushState({}, '', newUrl);

                    // Re-initialize any JavaScript components if needed
                    initializeCandidateComponents();
                },
                error: function(xhr, status, error) {
                    console.error('Filter error:', error);

                    // Hide loading
                    $('#candidates-loading').addClass('tw-hidden');
                    $('#candidates-content').removeClass('candidate-loading');
                    $('#filter-loading').addClass('tw-hidden');

                    // Show error message
                    alert('Terjadi kesalahan saat memuat data. Silakan coba lagi.');
                }
            });
        }

        // Initialize candidate components after AJAX load
        function initializeCandidateComponents() {
            // Re-initialize tooltips
            var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
            var tooltipList = tooltipTriggerList.map(function(tooltipTriggerEl) {
                return new bootstrap.Tooltip(tooltipTriggerEl);
            });

            // Handle pagination links with AJAX
            $(document).off('click', '#pagination-container .page-link').on('click', '#pagination-container .page-link', function(e) {
                e.preventDefault();

                let url = $(this).attr('href');
                if (!url) return;

                // Show loading
                $('#candidates-loading').removeClass('tw-hidden');
                $('#candidates-content').addClass('candidate-loading');

                // Scroll to top of candidate listings
                $('html, body').animate({
                    scrollTop: $('#candidates-content').offset().top - 100
                }, 300);

                // Fetch the new page content
                $.ajax({
                    url: url,
                    type: 'GET',
                    headers: {
                        'X-Requested-With': 'XMLHttpRequest'
                    },
                    success: function(response) {
                        // Update candidates content
                        $('#candidates-content').html(response);

                        // Hide loading
                        $('#candidates-loading').addClass('tw-hidden');
                        $('#candidates-content').removeClass('candidate-loading');

                        // Update URL without page reload
                        window.history.pushState({}, '', url);

                        // Re-initialize components
                        initializeCandidateComponents();
                    },
                    error: function() {
                        // Hide loading and show error message
                        $('#candidates-loading').addClass('tw-hidden');
                        $('#candidates-content').removeClass('candidate-loading');
                        alert('Terjadi kesalahan saat memuat data. Silakan coba lagi.');
                    }
                });
            });

            // Re-initialize any other components that might be needed
        }

        // Filter close functions
        function keywordClose() {
            $('[name="keyword"]').val('');
            applyFiltersWithAjax();
        }

        function keahlianClose() {
            $('[name="keahlian"]').val('');
            applyFiltersWithAjax();
        }

        function experienceClose() {
            $('[name="experience"]').val('');
            applyFiltersWithAjax();
        }

        function educationClose() {
            $('[name="education"]').val('');
            applyFiltersWithAjax();
        }

        function genderClose() {
            $('[name="gender"]').val('');
            applyFiltersWithAjax();
        }

        function ageClose() {
            $('[name="umur_min"]').val('');
            $('[name="umur_max"]').val('');
            applyFiltersWithAjax();
        }



        // filter function
        function Filter() {
            $('#candidate_search_form').submit();
        }
        // sorting
        $('#sortby').on('change', function() {
            $('#candidate_search_form').submit();
        })
        // perpage
        $('#perpage').on('change', function() {
            $('#candidate_search_form').submit();
        })
        // filter close
        function FilterClose(name) {
            $('[name="' + name + '"]').val('');
            $('#candidate_search_form').submit();
        }


        // candidate profile modal data by ajax
        function showCandidateProfileModal(username) {
            // Show loading on button
            const button = $(`[onclick*="showCandidateProfileModal('${username}')"]`);
            const originalText = button.html();

            $.ajax({
                url: "{{ route('website.candidate.profile.details') }}",
                type: "GET",
                data: {
                    username: username,
                    count_view: 1
                },
                beforeSend: function() {
                    // Show loading on button
                    button.find('.button-text').addClass('d-none');
                    button.find('.button-loading').removeClass('d-none');
                    button.prop('disabled', true);

                    // Show modal with loading
                    $('.candidate_profile_modal').html(`
                        <div class="d-flex justify-content-center align-items-center" style="height: 400px;">
                            <div class="spinner-border text-primary" role="status">
                                <span class="visually-hidden">Loading...</span>
                            </div>
                        </div>
                    `);
                    $('#candidate-profile-modal').modal('show');
                },
                success: function(response) {
                    if (!response.success) {
                        if (response.redirect_url) {
                            return Swal.fire({
                                title: 'Oops...',
                                text: response.message,
                                icon: 'error',
                                showCancelButton: true,
                                confirmButtonColor: '#3085d6',
                                cancelButtonColor: '#d33',
                                confirmButtonText: "{{ __('upgrade_plan') }}"
                            }).then((result) => {
                                if (result.value) {
                                    window.location.href = response.redirect_url;
                                }
                            })
                        } else {
                            return Swal.fire('Error', response.message, 'error');
                        }
                    }

                    let data = response.data;
                    let social = data.social_info
                    let candidate = response.data.candidate;

                    $('#candidate_id').val(data.candidate.id)
                    if (data.candidate.bookmarked) {
                        $('#removeBookmakCandidate').removeClass('d-none')
                        $('#bookmakCandidate').addClass('d-none')
                    } else {
                        $('#bookmakCandidate').removeClass('d-none')
                        $('#removeBookmakCandidate').addClass('d-none')
                    }

                    // Set message candidate
                    $('#message_candidate_id').val(data.candidate.id)
                    $('#message_candidate_name').text(data.name)

                    // Set age instead of profession for message candidate
                    if (data.candidate.birth_date) {
                        const birthDate = new Date(data.candidate.birth_date);
                        const today = new Date();
                        let age = today.getFullYear() - birthDate.getFullYear();
                        const monthDiff = today.getMonth() - birthDate.getMonth();
                        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                            age--;
                        }
                        $('#message_candidate_position').text(age + ' Tahun');
                    } else {
                        $('#message_candidate_position').text('Umur tidak diketahui');
                    }

                    $('#message_candidate_image').attr('src', data.candidate.photo || '')



                    data.name ? $('.candidate-profile-info h2').html(data.name) : '';
                    data.bio ? $('.tab-pane p').html(data.bio) : '';
                    data.candidate.photo ? $('#candidate_image').attr("src", data.candidate.photo) : '';

                    // Display age instead of profession
                    if (data.candidate.birth_date) {
                        const birthDate = new Date(data.candidate.birth_date);
                        const today = new Date();
                        let age = today.getFullYear() - birthDate.getFullYear();
                        const monthDiff = today.getMonth() - birthDate.getMonth();
                        if (monthDiff < 0 || (monthDiff === 0 && today.getDate() < birthDate.getDate())) {
                            age--;
                        }
                        $('.candidate-profile-info h4').html(age + ' Tahun');
                    } else {
                        $('.candidate-profile-info h4').html('Umur tidak diketahui');
                    }

                    data.candidate.bio ? $('.biography p').html(data.candidate.bio) : '';

                    if (data.candidate.status == 'available') {
                        $('.candidate-profile-info h6').removeClass('d-none');
                    }

                    //social media Link
                    candidate.social_info[0] ? $('#facebook').attr('href', candidate.social_info[0]['url']) :
                        '';
                    candidate.social_info[1] ? $('#twitter').attr('href', candidate.social_info[1]['url']) : '';
                    candidate.social_info[2] ? $('#youtube').attr('href', candidate.social_info[2]['url']) : '';
                    candidate.social_info[3] ? $('#linkedin').attr('href', candidate.social_info[3]['url']) :
                        '';
                    candidate.social_info[4] ? $('#pinterest').attr('href', candidate.social_info[4]['url']) :
                        '';
                    candidate.social_info[5] ? $('#github').attr('href', candidate.social_info[5]['url']) : '';

                    // Social info
                    if (social.facebook || social.twitter || social.linkedin || social.youtube || social
                        .instagram) {
                        $('#candidate_social_profile_modal').show()
                        social.facebook ? $('.social-media ul li:nth-child(1)').attr("href", social.facebook) :
                            '';
                        social.twitter ? $('.social-media ul li:nth-child(2)').attr("href", social.twitter) :
                            '';
                        social.linkedin ? $('.social-media ul li:nth-child(3)').attr("href", social.linkedin) :
                            '';
                        social.instagram ? $('.social-media ul li:nth-child(4)').attr("href", social
                                .instagram) :
                            '';
                        social.youtube ? $('.social-media ul li:nth-child(5)').attr("href", social.youtube) :
                            '';

                    } else {
                        $('#candidate_social_profile_modal').hide()
                    }

                    // skills & languages
                    if (candidate.skills.length != 0) {
                        $("#candidate_skills span").remove();
                        console.log(123)

                        $.each(candidate.skills, function(key, value) {
                            $('#candidate_skills').append(
                                `<span class="tw-bg-[#E7F0FA] tw-rounded-[4px] tw-text-sm tw-text-[#0A65CC] tw-px-3 tw-py-1.5">
                                    ${value.name}
                                </span>`
                            )
                        });
                    }

                    if (candidate.languages.length != 0) {
                        $("#candidate_languages div").remove();

                        $.each(candidate.languages, function(key, value) {
                            $('#candidate_languages').append(
                                `<div class="tw-inline-block tw-p-3 tw-bg-[#F1F2F4] tw-rounded-[4px]">
                                    <h4 class="tw-text-sm tw-text-[#474C54] tw-font-medium tw-mb-[2px]">${value.name}</h4>
                                </div>`

                            )
                        });
                    }

                    if (candidate.educations.length != 0) {
                        $("#candidate_profile_educations tr").remove();

                        $.each(candidate.educations, function(key, value) {
                            $('#candidate_profile_educations').append(
                                `<tr>
                                    <td>${value.level}</td>
                                    <td>${value.degree}</td>
                                    <td>${value.year}</td>
                                </tr>`
                            )
                        });
                    }

                    if (candidate.experiences.length != 0) {
                        $("#candidate_profile_experiences tr").remove();

                        $.each(candidate.experiences, function(key, value) {
                            let formatted_end = value.currently_working ? 'Currently Working' : value
                                .formatted_end
                            $('#candidate_profile_experiences').append(
                                `<tr>
                                    <td>${value.company}</td>
                                    <td>${value.department} / ${value.designation}</td>
                                    <td>${value.formatted_start} - ${formatted_end}</td>
                                </tr>
                                `
                            )
                        });
                    }

                    // other info - Bio (Tentang Saya)
                    if (data.candidate.bio) {
                        $('#candidate-bio').html(data.candidate.bio);
                    } else {
                        $('#candidate-bio').html('Tidak ada informasi tentang kandidat ini.');
                    }

                    // experience info
                    // Clear the experience list container before populating the data
                    $('#experience-list').empty();

                    // Check if experiences array exists and has items
                    if (data.candidate.experiences && data.candidate.experiences.length > 0) {
                        // Loop through the experiences array in reverse order
                        for (let i = data.candidate.experiences.length - 1; i >= 0; i--) {
                            const experience = data.candidate.experiences[i];

                            // Create HTML for each experience
                            const experienceHTML = `
                            <li class="tw-text-sm tw-flex tw-flex-col tw-gap-3">
                                <div class="tw-flex tw-flex-col tw-gap-1">
                                    <p class="tw-m-0 tw-text-sm tw-text-[#0066CC] tw-font-medium -tw-mt-1.5">${experience.formatted_start || ''}</p>
                                    <h4 class="tw-m-0 tw-text-sm tw-text-[#14181A] tw-font-medium">${experience.designation || ''}</h4>
                                    <p class="tw-m-0 tw-text-sm tw-text-[#707A7D]">${experience.company || ''}${experience.department ? '/' + experience.department : ''}</p>
                                </div>
                                <p class="tw-m-0 tw-text-sm tw-text-[#3C4649]">${experience.responsibilities || ''}</p>
                            </li>
                            `;

                            // Append the experience HTML to the list
                            $('#experience-list').append(experienceHTML);
                        }
                    } else {
                        // If no experiences, show a message
                        $('#experience-list').append('<li class="tw-text-sm">Tidak ada data pengalaman</li>');
                    }

                    // education info
                    $('#education-list').empty();

                    // Check if educations array exists and has items
                    if (data.candidate.educations && data.candidate.educations.length > 0) {
                        // Loop through the education array in reverse order
                        for (let i = data.candidate.educations.length - 1; i >= 0; i--) {
                            const education = data.candidate.educations[i];

                            // Create HTML for each education
                            const educationHtml = `
                                <li class="tw-text-sm tw-flex tw-flex-col tw-gap-3">
                                    <div class="tw-flex tw-flex-col tw-gap-1">
                                        <p class="tw-m-0 tw-text-sm tw-text-[#0066CC] tw-font-medium -tw-mt-1.5" id="candidate-edu-year">${education.year || ''}</p>
                                        <h4 class="tw-m-0 tw-text-sm tw-text-[#14181A] tw-font-medium">${education.degree || ''}</h4>
                                        <p class="tw-m-0 tw-text-sm tw-text-[#707A7D]">${education.degree || ''} / ${education.level || ''}</p>
                                    </div>
                                    <p class="tw-m-0 tw-text-sm tw-text-[#3C4649]">${education.notes || ''}</p>
                                </li>
                            `;

                            // Append the education HTML to the list
                            $('#education-list').append(educationHtml);
                        }
                    } else {
                        // If no educations, show a message
                        $('#education-list').append('<li class="tw-text-sm">Tidak ada data pendidikan</li>');
                    }

                    // Clear previous certifications
                    $('#certification-list').empty();

                    // Populate certifications
                    if (data.candidate && data.candidate.certifications && data.candidate.certifications.length > 0) {
                        $.each(data.candidate.certifications, function(index, certification) {
                            // Create HTML for each certification
                            const certificationHtml = `
                                <li class="tw-text-sm tw-flex tw-flex-col tw-gap-3">
                                    <div class="tw-flex tw-flex-col tw-gap-1">
                                        <h4 class="tw-m-0 tw-text-sm tw-text-[#14181A] tw-font-medium">${certification.name || ''}</h4>
                                        <p class="tw-m-0 tw-text-sm tw-text-[#707A7D]">${certification.organization || ''}</p>
                                        <p class="tw-m-0 tw-text-sm tw-text-[#0066CC] tw-font-medium -tw-mt-1.5">
                                            ${certification.formatted_issue_date || ''} ${certification.expiration_date ? '- ' + (certification.formatted_expiration_date || '') : ''}
                                        </p>
                                    </div>
                                    ${certification.credential_id ? '<p class="tw-m-0 tw-text-sm tw-text-[#3C4649]">ID: ' + certification.credential_id + '</p>' : ''}
                                    ${certification.credential_url ? '<p class="tw-m-0 tw-text-sm"><a href="' + certification.credential_url + '" target="_blank" class="tw-text-[#0066CC]">Lihat Kredensial</a></p>' : ''}
                                </li>
                            `;

                            // Append the certification HTML to the list
                            $('#certification-list').append(certificationHtml);
                        });
                    } else {
                        // If no certifications, show a message
                        $('#certification-list').append('<li class="tw-text-sm">Tidak ada data sertifikasi</li>');
                    }

                    data.candidate.birth_date ? $('#candidate_birth_date').html(data.candidate.birth_date) : '';
                    $('#candidate_nationality').html('Indonesia');
                    if (data.candidate.marital_status) {
                        const maritalStatusTranslated = data.candidate.marital_status.toLowerCase() === 'married' ? 'Menikah' :
                                                      (data.candidate.marital_status.toLowerCase() === 'single' ? 'Belum Menikah' :
                                                      capitalizeFirstLetter(data.candidate.marital_status));
                        $('#candidate_marital_status').html(maritalStatusTranslated);
                    }
                    if (data.candidate.gender) {
                        const genderTranslated = data.candidate.gender.toLowerCase() === 'male' ? 'Laki-laki' :
                                               (data.candidate.gender.toLowerCase() === 'female' ? 'Perempuan' :
                                               capitalizeFirstLetter(data.candidate.gender));
                        $('#candidate_gender').html(genderTranslated);
                    }
                    data.candidate.experience ? $('#candidate_experience').html(data.candidate.experience
                        .name) : ''
                    data.candidate.education ? $('#candidate_education').html(capitalizeFirstLetter(data
                        .candidate.education.name)) : ''

                    if (data.candidate.website) {
                        $('#candidate_website').attr('href', data.candidate.website);
                        $('#candidate_website').html(data.candidate.website);
                    }
                    $('#candidate_location').html(data.candidate.district ? data.candidate
                        .district : data.candidate.locality)

                    data.contact_info && data.contact_info.phone ? $('#candidate_phone').html(data.contact_info
                        .phone) : ''
                    data.contact_info && data.contact_info.secondary_phone ? $('#candidate_seconday_phone')
                        .html(data.contact_info
                            .secondary_phone) : ''
                    data.contact_info && data.contact_info.email ? $('#contact_info_email').html(data
                        .contact_info.email) : ''

                    if (data.candidate.whatsapp_number && data.candidate.whatsapp_number.length) {
                        $("#contact_whatsapp").attr("href", 'https://wa.me/' + data.candidate.whatsapp_number)
                        $('#contact_whatsapp_part').removeClass('d-none')
                    } else {
                        $('#contact_whatsapp_part').addClass('d-none')
                    }

                    $('#candidate-profile-modal').modal('show');
                    if (response.profile_view_limit && response.profile_view_limit.length) {
                        if (!response.data.candidate.already_view) {
                            toastr.success(response.profile_view_limit, 'Success');
                        }
                    }

                    $('#cv_view' + candidate.id).removeClass("d-none");
                },
                error: function(error) {
                    console.log(error.data);
                    Swal.fire('Error', 'Something Went Wrongs!', 'error');
                },
                complete: function() {
                    // Reset button state
                    button.find('.button-text').removeClass('d-none');
                    button.find('.button-loading').addClass('d-none');
                    button.prop('disabled', false);
                }
            });
        }
        // company bookmarked data by ajax
        function CompanyBookmark(candidate) {
            $.ajax({
                url: "{{ route('company.bookmark.category.index', ['ajax' => 1]) }}",
                type: "GET",
                data: {
                    _token: '{{ csrf_token() }}'
                },
                dataType: 'json',
                success: function(data) {

                    $('#categoryList').html('');
                    if (data.length > 0) {
                        $.each(data, function(key, value) {
                            $('#categoryList').append(`
                                <div class="saved-candidate tw-my-2">
                                    <label for="exampleRadios${value.id}" class="tw-flex tw-gap-3 tw-items-center tw-cursor-pointer">
                                        <input onclick="BookmarkCanidate(${candidate},${value.id})" type="radio" name="experience" value="6" id="exampleRadios${value.id}">
                                        <span class="tw-flex">${value.name}</span>
                                    </label>
                                </div>
                            `);
                        });
                    }
                }
            });

            $('#aemploye-profile').modal('show');
        };
        // candidate bookmarked data by ajax
        function BookmarkCanidate(id, cat) {
            var url = "{{ route('company.companybookmarkcandidate', ':id') }}";
            url = url.replace(':id', id);

            $.ajax({
                url: url,
                type: "POST",
                data: {
                    cat: cat,
                    _token: '{{ csrf_token() }}'
                },
                dataType: 'json',
                success: function(response) {
                    if (!response.success) {
                        if (response.redirect_url) {
                            return Swal.fire({
                                title: 'Oops...',
                                text: response.message,
                                icon: 'error',
                                showCancelButton: true,
                                confirmButtonColor: '#3085d6',
                                cancelButtonColor: '#d33',
                                confirmButtonText: "{{ __('upgrade_plan') }}"
                            }).then((result) => {
                                if (result.value) {
                                    window.location.href = response.redirect_url;
                                }
                            })
                        } else {
                            return Swal.fire('Error', response.message, 'error');
                        }
                    }

                    // location.reload();
                },
                error: function(data) {
                    location.reload();
                }
            });
        }
        // remove bookmark data by ajax
        $('#removeBookmakCandidate').on('click', function() {
            var url = "{{ route('company.companybookmarkcandidate', ':id') }}";
            url = url.replace(':id', $('#candidate_id').val());
            // alert($('#candidate_id').val())
            $.ajax({
                url: url,
                type: "POST",
                data: {
                    _token: '{{ csrf_token() }}'
                },
                success: function(response) {
                    $('#removeBookmakCandidate').addClass('d-none')
                    $('#bookmakCandidate').removeClass('d-none')
                    toastr.success('Candidate removed from bookmark list', 'Success')
                },
                error: function(data) {
                    // alert('Something went wrong')
                    location.reload();
                }
            });
        });
        // bookmarked candidate
        $('#bookmakCandidate').on('click', function() {
            CompanyBookmark($('#candidate_id').val())
            $('#candidate-profile-modal').modal('hide');
        });
        // capitalize
        function capitalizeFirstLetter(string) {
            return string[0].toUpperCase() + string.slice(1);
        }
        //tooltip
        $(function() {
            $('[data-toggle="tooltip"]').tooltip()
        })

        //  skilss select2 design
        $('#skills').select2({
            theme: 'bootstrap4',
            tags: true,
            placeholder: 'Select Skill'
        });

    </script>
@endsection
