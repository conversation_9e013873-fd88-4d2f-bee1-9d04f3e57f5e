@extends('frontend.layouts.app')

@section('title')
    {{ __('Tambah Flyer Lowongan Kerja') }}
@endsection

@section('main')
    <div class="dashboard-wrapper">
        <div class="container">
            <div class="row">
                {{-- Sidebar --}}
                <x-website.company.sidebar />
                <div class="col-lg-9">
                    <div class="dashboard-right">
                        <div class="dashboard-right-header">
                            <div class="d-flex align-items-center justify-content-between">
                                <div class="d-flex align-items-center">
                                    <span class="sidebar-open-nav d-lg-none me-3">
                                        <i class="ph-list"></i>
                                    </span>
                                    <h2 class="tw-text-2xl tw-font-medium tw-text-[#18191C] mb-0">
                                        {{ __('Tambah Flyer Lowongan Kerja') }}
                                    </h2>
                                </div>
                                <a href="{{ route('company.flyer.index') }}" class="btn btn-outline-primary">
                                    <i class="ph-arrow-left"></i> {{ __('Kembali') }}
                                </a>
                            </div>
                        </div>
                        <div class="dashboard-right-body mt-5">
                            <form action="{{ route('company.flyer.store') }}" method="POST" enctype="multipart/form-data">
                                @csrf
                                <div class="row">
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="company_name" class="form-label">{{ __('Nama Perusahaan') }} <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="company_name" name="company_name" value="{{ formatCompanyName($company) }}" disabled>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="hrd_name" class="form-label">{{ __('Nama HRD saat mendaftar') }} <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control" id="hrd_name" name="hrd_name" value="{{ $company->user->name }}" disabled>
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="phone_number" class="form-label">{{ __('Nomor HP/WhatsApp') }} <span class="text-danger">*</span></label>
                                            <input type="text" class="form-control @error('phone_number') is-invalid @enderror" id="phone_number" name="phone_number" value="{{ old('phone_number') }}" placeholder="Contoh: 628123456789">
                                            <small class="text-muted">Format: 628xxx (untuk nomor yang dimulai 08) atau 622xxx (untuk nomor yang dimulai 02)</small>
                                            @error('phone_number')
                                                <div class="invalid-feedback">
                                                    {{ $message }}
                                                </div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-6">
                                        <div class="form-group mb-3">
                                            <label for="image" class="form-label">{{ __('Upload Flyer') }} <span class="text-danger">*</span></label>
                                            <input type="file" name="image" class="dropify @error('image') is-invalid @enderror"
                                                data-allowed-file-extensions="jpg jpeg png gif"
                                                data-max-file-size="5M"
                                                data-default-file=""
                                                data-show-errors="true">
                                            <small class="text-muted">{{ __('Format: jpg, jpeg, png, gif. Maksimal: 5MB') }}</small>
                                            @error('image')
                                                <div class="invalid-feedback">
                                                    {{ $message }}
                                                </div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="form-group mb-3">
                                            <label for="additional_info" class="form-label">
                                                {{ __('Informasi Tambahan') }} (opsional)
                                                <i class="ph-question-circle text-primary ms-1 info-icon"
                                                   data-bs-toggle="tooltip"
                                                   data-bs-placement="top"
                                                   title="Klik untuk melihat informasi yang sebaiknya ada dalam flyer"
                                                   style="cursor: pointer; font-size: 16px; vertical-align: middle;"
                                                   onclick="showFlyerInfoModal()"></i>
                                            </label>
                                            <textarea class="form-control @error('additional_info') is-invalid @enderror" id="additional_info" name="additional_info" rows="5" maxlength="500" placeholder="Contoh: Syarat khusus, benefit tambahan, atau informasi penting lainnya">{{ old('additional_info') }}</textarea>
                                            <div class="d-flex justify-content-end">
                                                <small class="text-muted"><span id="char-count">0</span>/500 karakter</small>
                                            </div>
                                            @error('additional_info')
                                                <div class="invalid-feedback">
                                                    {{ $message }}
                                                </div>
                                            @enderror
                                        </div>
                                    </div>
                                    <div class="col-md-12">
                                        <div class="form-group mb-3">
                                            <button type="submit" class="btn btn-primary">{{ __('Kirim') }}</button>
                                            <button type="reset" class="btn btn-secondary">{{ __('Reset') }}</button>
                                        </div>
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('frontend_scripts')
<link rel="stylesheet" href="{{ asset('backend') }}/plugins/dropify/css/dropify.min.css">
<script src="{{ asset('backend') }}/plugins/dropify/js/dropify.min.js"></script>
<script>
    $(document).ready(function() {
        // Initialize Dropify
        $('.dropify').dropify({
            messages: {
                'default': 'Seret dan lepas file di sini atau klik',
                'replace': 'Seret dan lepas atau klik untuk mengganti',
                'remove': 'Hapus',
                'error': 'Oops, terjadi kesalahan.'
            }
        });

        // Character counter
        $('#additional_info').on('input', function() {
            var charCount = $(this).val().length;
            $('#char-count').text(charCount);
        });

        // Format phone number
        $('#phone_number').on('input', function() {
            var phone = $(this).val().replace(/\D/g, '');

            // Limit to 14 digits
            if (phone.length > 14) {
                phone = phone.substring(0, 14);
            }

            // Format from 08 to 628 and 02 to 622
            if (phone.startsWith('08')) {
                phone = '628' + phone.substring(2);
            } else if (phone.startsWith('02')) {
                phone = '622' + phone.substring(2);
            }

            $(this).val(phone);
        });

        // Reset form and show success message
        $('button[type="reset"]').on('click', function() {
            $('#additional_info').val('');
            $('#char-count').text('0');
            $('.dropify-clear').click(); // Clear dropify
        });

        // Mobile sidebar toggle
        $('.sidebar-open-nav').on('click', function() {
            $('.d-sidebar').addClass('active');
            $('body').addClass('sidebar-overlay');
        });

        $('.close-sidebar').on('click', function() {
            $('.d-sidebar').removeClass('active');
            $('body').removeClass('sidebar-overlay');
        });

        $(document).on('click', function(e) {
            if ($(e.target).closest('.d-sidebar').length === 0 && $(e.target).closest('.sidebar-open-nav').length === 0) {
                $('.d-sidebar').removeClass('active');
                $('body').removeClass('sidebar-overlay');
            }
        });

        // Initialize tooltips
        var tooltipTriggerList = [].slice.call(document.querySelectorAll('[data-bs-toggle="tooltip"]'));
        var tooltipList = tooltipTriggerList.map(function (tooltipTriggerEl) {
            return new bootstrap.Tooltip(tooltipTriggerEl);
        });
    });

    // Function to show flyer info modal
    function showFlyerInfoModal() {
        Swal.fire({
            title: 'Informasi yang Sebaiknya Ada dalam Flyer',
            html: `
                <div class="text-start">
                    <h6 class="text-primary mb-3">📋 Informasi Wajib:</h6>
                    <ul class="mb-4">
                        <li><strong>Judul/Posisi Pekerjaan</strong> - Nama jabatan yang dibutuhkan</li>
                        <li><strong>Kategori Pekerjaan</strong> - Bidang industri/kategori</li>
                        <li><strong>Tingkat Pendidikan</strong> - Minimal pendidikan yang dibutuhkan</li>
                        <li><strong>Pengalaman Kerja</strong> - Minimal pengalaman yang dibutuhkan</li>
                        <li><strong>Tipe Pekerjaan</strong> - Full time, Part time, Kontrak, dll</li>
                        <li><strong>Jumlah Lowongan</strong> - Berapa orang yang dibutuhkan</li>
                        <li><strong>Lokasi Kerja</strong> - Alamat atau area kerja</li>
                        <li><strong>Batas Lamaran</strong> - Deadline pendaftaran</li>
                        <li><strong>Kontak</strong> - Nomor HP/WhatsApp yang bisa dihubungi</li>
                    </ul>

                    <h6 class="text-success mb-3">💡 Informasi Tambahan (Opsional):</h6>
                    <ul>
                        <li><strong>Gaji/Range Gaji</strong> - Besaran atau rentang gaji</li>
                        <li><strong>Benefit</strong> - Tunjangan, asuransi, bonus, dll</li>
                        <li><strong>Jam Kerja</strong> - Waktu kerja dan hari kerja</li>
                        <li><strong>Syarat Khusus</strong> - Usia, gender, tinggi badan, dll</li>
                        <li><strong>Skill/Keahlian</strong> - Kemampuan teknis yang dibutuhkan</li>
                        <li><strong>Deskripsi Pekerjaan</strong> - Tugas dan tanggung jawab</li>
                        <li><strong>Cara Melamar</strong> - Email, website, atau datang langsung</li>
                        <li><strong>Proses Seleksi</strong> - Tahapan interview atau tes</li>
                    </ul>

                    <div class="alert alert-info mt-3">
                        <small><i class="ph-info-circle me-1"></i> Semakin lengkap informasi, semakin mudah kandidat memahami lowongan Anda!</small>
                    </div>
                </div>
            `,
            icon: 'info',
            confirmButtonText: 'Mengerti',
            width: '700px',
            customClass: {
                popup: 'flyer-info-modal'
            }
        });
    }
</script>
<style>
    @media (max-width: 991px) {
        .d-sidebar {
            position: fixed;
            top: 0;
            left: -300px;
            width: 300px;
            height: 100vh;
            background-color: #fff;
            z-index: 9999;
            transition: all 0.3s ease;
            overflow-y: auto;
            padding: 20px;
            box-shadow: 0 0 15px rgba(0, 0, 0, 0.1);
        }

        .d-sidebar.active {
            left: 0;
        }

        body.sidebar-overlay:before {
            content: '';
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            background-color: rgba(0, 0, 0, 0.5);
            z-index: 999;
        }

        .close-sidebar {
            display: block;
            cursor: pointer;
        }

        .sidebar-open-nav {
            display: block;
            cursor: pointer;
            font-size: 24px;
        }
    }

    /* Info icon styling */
    .info-icon {
        display: inline-block !important;
        font-weight: 900 !important;
        color: #0A65CC !important;
        transition: all 0.3s ease;
    }

    .info-icon:hover {
        color: #084298 !important;
        transform: scale(1.1);
    }

    /* Modal styling */
    .flyer-info-modal ul {
        padding-left: 1.2rem;
    }

    .flyer-info-modal li {
        margin-bottom: 0.5rem;
        line-height: 1.4;
    }

    .flyer-info-modal .alert {
        border-radius: 8px;
        border: none;
        background-color: #e7f3ff;
        color: #0c5460;
    }
</style>
@endsection
