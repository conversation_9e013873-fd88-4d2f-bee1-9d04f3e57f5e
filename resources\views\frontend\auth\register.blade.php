@extends('frontend.auth.layouts.auth')

@section('meta')
    @php
        $data = metaData('register');
    @endphp
@endsection

@section('description')
    {{ $data->description }}
@endsection

@section('title')
    {{ __('register') }}
@endsection

@section('og:image')
    {{ asset($data->image) }}
@endsection

@section('css')
<link rel="stylesheet" href="//code.jquery.com/ui/1.13.2/themes/base/jquery-ui.css">
@endsection

@section('content')
<div class="row">
    <div class="auth-page2 order-1 order-lg-0">
        <div class="rt-spacer-100 rt-spacer-lg-50 rt-spacer-xs-50"></div>
        <div class="rt-spacer-100 rt-spacer-lg-50 rt-spacer-xs-0"></div>
        <div class="rt-spacer-50 rt-spacer-lg-0 rt-spacer-xs-0"></div>
        <div class="container">
            <div class="row ">
                <div class="col-xl-5 col-lg-6 col-md-12 tw-bg-white tw-relative tw-z-50">
                    <div class="auth-box2">
                        <form id="registrationForm" action="{{ route('register') }}" method="POST" class="rt-form" enctype="multipart/form-data">
                            @csrf
                            <div class="row">
                                <div class="col-12">
                                    <h4 class="rt-mb-20">{{ __('create_account') }}</h4>
                                    <span class="d-block body-font-3 text-gray-600 rt-mb-32">
                                        {{ __('already_have_account') }}
                                        <span>
                                            <a href="{{ route('login') }}">{{ __('log_in') }}</a>
                                        </span>
                                    </span>
                                </div>
                                <div class="col-12">
                                    <div class="tw-bg-[#F1F2F4] tw-rounded-lg tw-mb-6 tw-p-3">
                                        <p class="tw-text-[#767F8C] tw-text-xs tw-font-medium tw-text-center tw-mb-2">
                                            {{ __('create_account_as_a') }}
                                        </p>
                                        <div class="switcher-container tw-px-0 tw-w-full tw-border-2 tw-border-red-600 tw-flex">
                                            <input id="switcher-toggle-on" class="switcher-toggle switcher-toggle-left tw-w-full" name="role" value="candidate" type="radio" {{ old('role', 'candidate') == 'candidate' ? 'checked' : '' }}>
                                            <label for="switcher-toggle-on" class="switcher-button tw-w-full tw-rounded-tl-md tw-rounded-bl-md" id="web-btn">
                                                <span><x-svg.candidate-profile-icon /></span>
                                                <span>{{ __('candidate') }}</span>
                                            </label>
                                            <input id="switcher-toggle-off" class="switcher-toggle switcher-toggle-right tw-w-full" name="role" value="company" type="radio" {{ old('role') == 'company' ? 'checked' : '' }}>
                                            <label for="switcher-toggle-off" class="switcher-button tw-w-full tw-rounded-tr-md tw-rounded-br-md" id="wp-btn">
                                                <span><x-svg.employer-profile-icon /></span>
                                                <span>{{ __('employer') }}</span>
                                            </label>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Data Diri Section -->
                            <div class="row">
                                <!-- Dropdown Badan Hukum (hanya muncul saat toggle perusahaan dipilih) -->
                                <div class="col-lg-12 company-fields" style="display: none;">
                                    <div class="fromGroup rt-mb-15">
                                        <select name="organization_type" id="organization_type" class="field form-control @error('organization_type') is-invalid @enderror">
                                            <option value="">{{ __('Pilih Badan Hukum') }}</option>
                                            @php
                                                $organization_types = DB::table('organization_types')
                                                    ->join('organization_type_translations', 'organization_types.id', '=', 'organization_type_translations.organization_type_id')
                                                    ->where('organization_type_translations.locale', 'id')
                                                    ->select('organization_types.id', 'organization_type_translations.name', 'organization_type_translations.prefix')
                                                    ->get();
                                            @endphp
                                            @foreach($organization_types as $type)
                                                <option value="{{ $type->id }}" data-prefix="{{ $type->prefix }}" {{ old('organization_type') == $type->id ? 'selected' : '' }}>{{ $type->name }}</option>
                                            @endforeach
                                        </select>
                                        @error('organization_type')
                                            <span class="invalid-feedback" role="alert">{{ __($message) }}</span>
                                        @enderror
                                    </div>
                                </div>
                                <!-- Input Nama -->
                                <div class="col-lg-12">
                                    <div class="fromGroup rt-mb-15">
                                        <input name="name" id="name" value="{{ old('name') }}" class="field form-control @error('name') is-invalid @enderror" type="text" placeholder="{{ __('full_name') }}">
                                        <small id="nama-info" class="text-danger fst-italic"></small>
                                        <input type="hidden" name="full_company_name" id="full_company_name" value="{{ old('full_company_name') }}">
                                        @error('name')
                                            <span class="invalid-feedback" role="alert">{{ __($message) }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                                <div class="col-lg-12">
                                    <div class="fromGroup rt-mb-15">
                                        <input name="nik" id="nik" value="{{ old('nik') }}"
                                            class="field form-control @error('nik') is-invalid @enderror"
                                            type="number" placeholder="{{ __('nik') }}" oninput="limitDigits(this)">
                                            <small id="nik-info" class="text-danger fst-italic">NIK: 16 Digit</small>
                                        @error('nik')
                                            <span class="invalid-feedback" role="alert">{{ __($message) }}</span>
                                        @enderror
                                    </div>
                                </div>
                                {{-- <div class="col-lg-12">
                                    <div class="fromGroup rt-mb-15">
                                        @livewire('country-state-city')
                                        @error('location')
                                            <span class="ml-3 text-md text-danger">{{ $message }}</span>
                                        @enderror
                                    </div>
                                </div> --}}
                                <div class="col-lg-12">
                                    <div class="fromGroup rt-mb-15">
                                        <select name="provinsi" id="provinsi" class="field form-control @error('provinsi') is-invalid @enderror" onchange="fetchCities(this.value)">
                                            <option value="">{{ __('Pilih Provinsi') }}</option>
                                        </select>
                                        <input type="hidden" id="hidden_province" name="hidden_province">
                                        @error('provinsi')
                                            <span class="invalid-feedback" role="alert">{{ __($message) }}</span>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-lg-12">
                                    <div class="fromGroup rt-mb-15">
                                        <select name="kabupaten_kota" id="kabupaten_kota" class="field form-control @error('kabupaten_kota') is-invalid @enderror" onchange="fetchDistricts(this.value)">
                                            <option value="">{{ __('Pilih Kab/Kota') }}</option>
                                        </select>
                                        <input type="hidden" id="hidden_city" name="hidden_city">
                                        @error('kabupaten_kota')
                                            <span class="invalid-feedback" role="alert">{{ __($message) }}</span>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-lg-12">
                                    <div class="fromGroup rt-mb-15">
                                        <select name="kecamatan" id="kecamatan" class="field form-control @error('kecamatan') is-invalid @enderror" onchange="fetchVillages(this.value)">
                                            <option value="">{{ __('Pilih Kecamatan') }}</option>
                                        </select>
                                        <input type="hidden" id="hidden_district" name="hidden_district">
                                        @error('kecamatan')
                                            <span class="invalid-feedback" role="alert">{{ __($message) }}</span>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-lg-12">
                                    <div class="fromGroup rt-mb-15">
                                        <select name="kelurahan" id="kelurahan" class="field form-control @error('kelurahan') is-invalid @enderror" onchange="saveVillage(this.value)">
                                            <option value="">{{ __('Pilih Kelurahan') }}</option>
                                        </select>
                                        <input type="hidden" id="hidden_village" name="hidden_village">
                                        @error('kelurahan')
                                            <span class="invalid-feedback" role="alert">{{ __($message) }}</span>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-lg-12">
                                    <div class="fromGroup rt-mb-15">
                                        <textarea name="alamat_ktp" id="alamat_ktp" rows="3"
                                            class="field form-control @error('alamat_ktp') is-invalid @enderror"
                                            placeholder="{{ __('address_ktp') }}">{{ old('alamat_ktp') }}</textarea>
                                        @error('alamat_ktp')
                                            <span class="invalid-feedback" role="alert">{{ __($message) }}</span>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-lg-12 form-pelamar">
                                    <div class="fromGroup rt-mb-15">
                                        <input name="tempat_lahir" id="tempat_lahir" value="{{ old('tempat_lahir') }}"
                                            class="field form-control @error('tempat_lahir') is-invalid @enderror"
                                            type="text" placeholder="{{ __('birth_place') }}">
                                        @error('tempat_lahir')
                                            <span class="invalid-feedback" role="alert">{{ __($message) }}</span>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-lg-12 form-pelamar">
                                    <div class="fromGroup rt-mb-15">
                                        <input name="tanggal_lahir" id="tanggal_lahir" value="{{ old('tanggal_lahir') }}"
                                            class="w-full px-4 py-2 border border-gray-300 rounded-lg focus:outline-none focus:border-blue-500
                                                @error('tanggal_lahir') is-invalid @enderror"
                                            type="text" placeholder="Pilih Tanggal Lahir">
                                        @error('tanggal_lahir')
                                            <span class="invalid-feedback" role="alert">{{ __($message) }}</span>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-lg-12 form-pelamar">
                                    <div class="fromGroup rt-mb-15">
                                        <select name="jenis_kelamin" id="jenis_kelamin"
                                            class="field form-control @error('jenis_kelamin') is-invalid @enderror">
                                            <option value="">{{ __('select_gender') }}</option>
                                            <option value="Laki-laki" {{ old('jenis_kelamin') == 'Laki-laki' ? 'selected' : '' }}>
                                                {{ __('male') }}</option>
                                            <option value="Perempuan" {{ old('jenis_kelamin') == 'Perempuan' ? 'selected' : '' }}>
                                                {{ __('female') }}</option>
                                        </select>
                                        @error('jenis_kelamin')
                                            <span class="invalid-feedback" role="alert">{{ __($message) }}</span>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-lg-12 form-pelamar">
                                    <div class="fromGroup rt-mb-15">
                                        <select name="status_perkawinan" id="status_perkawinan"
                                            class="field form-control @error('status_perkawinan') is-invalid @enderror">
                                            <option value="">{{ __('select_marital_status') }}</option>
                                            <option value="Belum Menikah" {{ old('status_perkawinan') == 'Belum Menikah' ? 'selected' : '' }}>
                                                {{ __('single') }}</option>
                                            <option value="Menikah" {{ old('status_perkawinan') == 'Menikah' ? 'selected' : '' }}>
                                                {{ __('married') }}</option>
                                        </select>
                                        @error('status_perkawinan')
                                            <span class="invalid-feedback" role="alert">{{ __($message) }}</span>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-lg-12 form-pelamar">
                                    <div class="fromGroup rt-mb-15">
                                        <select name="agama" id="agama"
                                            class="field form-control @error('agama') is-invalid @enderror">
                                            <option value="">{{ __('select_religion') }}</option>
                                            <option value="Islam" {{ old('agama') == 'Islam' ? 'selected' : '' }}>
                                                {{ __('islam') }}</option>
                                            <option value="Kristen" {{ old('agama') == 'Kristen' ? 'selected' : '' }}>
                                                {{ __('christian') }}</option>
                                            <option value="Hindu" {{ old('agama') == 'Hindu' ? 'selected' : '' }}>
                                                {{ __('hindu') }}</option>
                                            <option value="Buddha" {{ old('agama') == 'Buddha' ? 'selected' : '' }}>
                                                {{ __('buddhist') }}</option>
                                            <option value="Konghucu" {{ old('agama') == 'Konghucu' ? 'selected' : '' }}>
                                                {{ __('confucian') }}</option>
                                            <option value="Lainnya" {{ old('agama') == 'Lainnya' ? 'selected' : '' }}>
                                                {{ __('other') }}</option>
                                        </select>
                                        @error('agama')
                                            <span class="invalid-feedback" role="alert">{{ __($message) }}</span>
                                        @enderror
                                    </div>
                                </div>

                                <div class="col-lg-12 form-pelamar">
                                    <div class="fromGroup rt-mb-15">
                                        <select name="pendidikan_terakhir" id="pendidikan_terakhir"
                                            class="field form-control @error('pendidikan_terakhir') is-invalid @enderror">
                                            <option value="">{{ __('select_education_level') }}</option>
                                            <option value="SD" {{ old('pendidikan_terakhir') == 'SD' ? 'selected' : '' }}>
                                                {{ __('elementary_school') }}</option>
                                            <option value="SMP" {{ old('pendidikan_terakhir') == 'SMP' ? 'selected' : '' }}>
                                                {{ __('junior_high_school') }}</option>
                                            <option value="SMA" {{ old('pendidikan_terakhir') == 'SMA' ? 'selected' : '' }}>
                                                {{ __('senior_high_school') }}</option>
                                            <option value="Diploma" {{ old('pendidikan_terakhir') == 'Diploma' ? 'selected' : '' }}>
                                                {{ __('diploma') }}</option>
                                            <option value="Sarjana" {{ old('pendidikan_terakhir') == 'Sarjana' ? 'selected' : '' }}>
                                                {{ __('bachelor') }}</option>
                                            <option value="Magister" {{ old('pendidikan_terakhir') == 'Magister' ? 'selected' : '' }}>
                                                {{ __('master') }}</option>
                                            <option value="Doktor" {{ old('pendidikan_terakhir') == 'Doktor' ? 'selected' : '' }}>
                                                {{ __('doctorate') }}</option>
                                        </select>
                                        @error('pendidikan_terakhir')
                                            <span class="invalid-feedback" role="alert">{{ __($message) }}</span>
                                        @enderror
                                    </div>
                                </div>
                                <!-- Detail Penanggung Jawab Perusahaan -->
                                <div class="col-lg-12 company-fields" style="display: none;">
                                    <div class="fromGroup rt-mb-15">
                                        <input name="nama_hrd" id="nama_hrd" value="{{ old('nama_hrd') }}"
                                            class="field form-control @error('nama_hrd') is-invalid @enderror"
                                            type="text" placeholder="{{ __('Nama Penanggung Jawab') }}">
                                        @error('nama_hrd')
                                            <span class="invalid-feedback" role="alert">{{ __($message) }}</span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-lg-12 company-fields" style="display: none;">
                                    <div class="fromGroup rt-mb-15">
                                        <input name="jabatan" id="jabatan" value="{{ old('jabatan') }}"
                                            class="field form-control @error('jabatan') is-invalid @enderror"
                                            type="text" placeholder="{{ __('Jabatan') }}">
                                        @error('jabatan')
                                            <span class="invalid-feedback" role="alert">{{ __($message) }}</span>
                                        @enderror
                                    </div>
                                </div>
                                <!-- No Telepon -->
                                <div class="col-lg-12">
                                    <div class="fromGroup rt-mb-15">
                                        <input name="no_hp" id="no_hp" value="{{ old('no_hp') }}"
                                            class="field form-control @error('no_hp') is-invalid @enderror"
                                            type="number" placeholder="{{ __('phone_number') }}" oninput="limitPhoneDigits(this)">
                                            <small class="text-danger font-italic">Contoh: 62856xxxxxxxx (Maksimal 14 digit)</small>
                                        @error('no_hp')
                                            <span class="invalid-feedback" role="alert">{{ __($message) }}</span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-lg-12" id="ak1-upload-container">
                                    <div class="form-group rt-mb-15">
                                        <label id="ak1Label" for="ak1">Upload Foto <span class="text-danger text-decoration-underline fw-bold">AK1 (Kartu Kuning)</span> atau <span class="text-danger text-decoration-underline fw-bold">KTP</span> (jika belum memiliki kartu kuning)<br>
                                            <small class="text-danger fst-italic">Format: jpg/jpeg/png/pdf. Max 10MB.</small>
                                        </label>
                                        <input type="file" name="ak1" id="ak1" accept=".jpeg, .png, .jpg, .pdf"
                                            class="dropify form-control @error('ak1') is-invalid @enderror"
                                            data-show-errors="true" data-max-file-size="10M">
                                        @error('ak1')
                                            <span class="invalid-feedback" role="alert">{{ __($message) }}</span>
                                        @enderror
                                    </div>
                                </div>
                                <div class="col-lg-12">
                                    <div class="fromGroup rt-mb-15">
                                        <input name="email" id="email" value="{{ old('email') }}"
                                            class="field form-control @error('email') is-invalid @enderror"
                                            type="email" placeholder="{{ __('email') }}">
                                        @error('email')
                                            <span class="invalid-feedback" role="alert">{{ __($message) }}</span>
                                        @enderror
                                    </div>
                                </div>
                            </div>

                            <!-- Password Section -->
                            <div class="rt-mb-15">
                                <div class="d-flex fromGroup">
                                    <input name="password" id="password"
                                        class="form-control @error('password') is-invalid @enderror" type="password"
                                        placeholder="{{ __('password') }}">
                                    <div onclick="passToText('password','eyeIcon')" id="eyeIcon" class="has-badge">
                                        <i class="ph-eye @error('password') m-3 @enderror"></i>
                                    </div>
                                </div>
                                @error('password')
                                    <span class="text-danger" role="alert">{{ __($message) }}</span>
                                @enderror
                            </div>
                            <div class="rt-mb-15">
                                <div class="d-flex fromGroup">
                                    <input name="password_confirmation" id="password_confirmation"
                                        class="form-control @error('password_confirmation') is-invalid @enderror"
                                        type="password" placeholder="{{ __('confirm_password') }}">
                                    <div onclick="passToText('password_confirmation','eyeIcon2')" id="eyeIcon2"
                                        class="has-badge">
                                        <i class="ph-eye @error('password_confirmation') m-3 @enderror"></i>
                                    </div>
                                </div>
                                @error('password_confirmation')
                                    <span class="text-danger" role="alert">{{ __($message) }}</span>
                                @enderror
                            </div>

                            <!-- Captcha Section -->
                            @if (config('captcha.active'))
                                <div class="rt-mb-15">
                                    <div class="g-custom-css">
                                        {!! NoCaptcha::display() !!}
                                    </div>
                                    @if ($errors->has('g-recaptcha-response'))
                                        <span class="text-danger text-sm">
                                            <strong>{{ $errors->first('g-recaptcha-response') }}</strong>
                                        </span>
                                    @endif
                                </div>
                            @endif

                            <!-- Terms and Conditions -->
                            <div class="rt-mb-30">
                                <div class="form-check from-chekbox-custom align-items-center">
                                    <input id="term" class="form-check-input" type="checkbox" value="1" required>
                                    <label class="form-check-label pointer text-gray-700 f-size-14" for="term">
                                        {{ __('Saya memastikan data yang saya berikan adalah BENAR.') }}
                                    </label>
                                    <a href="{{ url('syarat-dan-ketentuan') }}" target="_blank"
                                        class="body-font-4 text-primary-500">
                                        {{ __('Syarat dan Ketentuan') }}
                                    </a>
                                    •
                                    <a href="{{ url('kebijakan-privasi') }}" target="_blank"
                                        class="body-font-4 text-primary-500">
                                        {{ __('Kebijakan Privasi') }}
                                    </a>
                                </div>
                            </div>

                            <!-- Submit Button -->
                            <button id="submitButton" type="submit" class="btn btn-primary d-block rt-mb-15">
                                <span class="button-content-wrapper ">
                                    <span class="button-icon align-icon-right">
                                        <x-svg.rightarrow-icon />
                                    </span>
                                    <span class="button-text">
                                        {{ __('create_account') }}
                                    </span>
                                </span>
                            </button>
                        </form>
                        @if ($errors->any())
                            <div class="alert alert-danger">
                                <ul>
                                    @foreach ($errors->all() as $error)
                                        <li>{{ $error }}</li>
                                    @endforeach
                                </ul>
                            </div>
                        @endif

                                @php
                                    $google = config('services.google.active') && config('services.google.client_id') && config('services.google.client_secret');
                                    $facebook = config('services.facebook.active') && config('services.facebook.client_id') && config('services.facebook.client_secret');
                                    $twitter = config('services.twitter.active') && config('services.twitter.client_id') && config('services.twitter.client_secret');
                                    $linkedin = config('services.linkedin-openid.active') && config('services.linkedin-openid.client_id') && config('services.linkedin-openid.client_secret');
                                    $github = config('services.github.active') && config('services.github.client_id') && config('services.github.client_secret');
                                @endphp
                                <div>
                                    @if ($google || $facebook || $twitter || $linkedin || $github)
                                        <p class="or text-center">{{ __('or') }}</p>
                                    @endif
                                    <div class="tw-grid tw-grid-cols-1 sm:tw-grid-cols-3 tw-gap-6">
                                        @if ($google)
                                            <div>
                                                <a href="{{ route('social.login', 'google') }}"
                                                    class="btn btn-outline-plain d-block custom-padding me-3 rt-mb-xs-10 ">
                                                    <span class="button-content-wrapper">
                                                        <span class="button-icon align-icon-left">
                                                            <x-svg.google-icon />
                                                        </span>
                                                        <span class="button-text">
                                                            {{ __('google') }}
                                                        </span>
                                                    </span>
                                                </a>
                                            </div>
                                        @endif
                                        @if ($facebook)
                                            <div>
                                                <a href="{{ route('social.login', 'facebook') }}"
                                                    class="btn btn-outline-plain d-block custom-padding me-3 rt-mb-xs-10 ">
                                                    <span class="button-content-wrapper ">
                                                        <span class="button-icon align-icon-left">
                                                            <x-svg.facebook-icon />
                                                        </span>
                                                        <span class="button-text">
                                                            {{ __('facebook') }}
                                                        </span>
                                                    </span>
                                                </a>
                                            </div>
                                        @endif
                                        @if ($twitter)
                                            <div>
                                                <a href="{{ route('social.login', 'twitter') }}"
                                                    class="btn btn-outline-plain d-block custom-padding me-3 rt-mb-xs-10 ">
                                                    <span class="button-content-wrapper ">
                                                        <span class="button-icon align-icon-left">
                                                            <x-svg.twitter-icon fill="#007ad9" />
                                                        </span>
                                                        <span class="button-text">
                                                            {{ __('twitter') }}
                                                        </span>
                                                    </span>
                                                </a>
                                            </div>
                                        @endif
                                        @if ($linkedin)
                                            <div class="d-flex justify-content-center col-12 rt-mb-15">
                                                <button onclick="LoginService('linkedin-openid')" type="button"
                                                    class="w-100 btn btn-outline-plain custom-padding ">
                                                    <span class="button-content-wrapper ctr">
                                                        <span class="button-icon align-icon-left">
                                                            <x-svg.linkedin-icon />
                                                        </span>
                                                        <span class="button-text">
                                                            {{ __('linkedin') }}
                                                        </span>
                                                    </span>
                                                </a>
                                            </div>
                                        @endif
                                        @if ($github)
                                            <div>
                                                <a href="{{ route('social.login', 'github') }}"
                                                    class="btn btn-outline-plain d-block custom-padding me-3 rt-mb-xs-10 ">
                                                    <span class="button-content-wrapper ">
                                                        <span class="button-icon align-icon-left">
                                                            <x-svg.github-icon />
                                                        </span>
                                                        <span class="button-text">
                                                            {{ __('github') }}
                                                        </span>
                                                    </span>
                                                </a>
                                            </div>
                                        @endif
                                    </div>
                                </div>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
            <div class="rt-spacer-100 rt-spacer-md-50"></div>
        </div>
        <div class="auth-right-sidebar reg-sidebar order-1 order-lg-0">
            <div class="sidebar-bg" style="background-image: url({{ asset($cms_setting->register_page_image) }});">
                <div class="sidebar-content">
                    <h4 class="text-gray-10 rt-mb-50">{{ openJobs() }} {{ __('open_jobs_waiting_for_you') }}</h4>
                    <div class="d-flex">
                        <div class="flex-grow-1 rt-mb-24">
                            <div class="card jobcardStyle1 counterbox4">
                                <div class="card-body">
                                    <div class="rt-single-icon-box icon-center2">
                                        <div class="icon-thumb">
                                            <div class="icon-64">
                                                <x-svg.livejob-icon />
                                            </div>
                                        </div>
                                        <div class="iconbox-content">
                                            <div class="f-size-20 ft-wt-5"><span
                                                    class="counter">{{ openJobs() }}</span>
                                            </div>
                                            <span class=" f-size-14">{{ __('live_job') }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="flex-grow-1  rt-mb-24">
                            <div class="card jobcardStyle1 counterbox4">
                                <div class="card-body">
                                    <div class="rt-single-icon-box icon-center2">
                                        <div class="icon-thumb">
                                            <div class="icon-64">
                                                <x-svg.thumb-icon />
                                            </div>
                                        </div>
                                        <div class="iconbox-content">
                                            <div class="f-size-20 ft-wt-5"><span
                                                    class="counter">{{ companies() }}</span>
                                            </div>
                                            <span class=" f-size-14">{{ __('companies') }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="flex-grow-1 rt-mb-24">
                            <div class="card jobcardStyle1 counterbox4">
                                <div class="card-body">
                                    <div class="rt-single-icon-box icon-center2">
                                        <div class="icon-thumb">
                                            <div class="icon-64">
                                                <x-svg.newjobs-icon />
                                            </div>
                                        </div>
                                        <div class="iconbox-content">
                                            <div class="f-size-20 ft-wt-5"><span
                                                class="counter">{{ $candidates }}</span>
                                            </div>
                                            <span class=" f-size-14">{{ __('candidates') }}</span>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
    <!-- The Modal -->
    <div id="ModalBtn" class="modal">
        <div class="row justify-content-center m-2 mt-5 pt-5">
            <div class="col-sm-12 col-lg-4">
                <div class="rt-rounded-12">
                    <div class="card border border-gray-500">
                        <div class="card-header bg-primary text-white font-size-25">
                            {{ __('select_one') }}
                        </div>
                        <form id="LoginFormHit" class="d-inline justify-content-center" method="GET">
                            <div class="card-body">
                                <div class="col-md-6">
                                    <div class="form-group">
                                        <label class="form-label"
                                            for="experience">{{ __('employer_or_candidate') }}</label>
                                        <select name="user" class="form-controll rounded" id="">
                                            <option value="candidate">{{ __('candidate') }}</option>
                                            <option value="company">{{ __('employer') }}</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                            <div class="card-footer d-flex justify-content-between">
                                <button onclick="CloseModoal()" type="button" class="close btn btn-danger">
                                    <div class="button-content-wrapper ">
                                        <span class="button-text">
                                            {{ __('cancel') }}
                                        </span>
                                    </div>
                                </button>
                                <button type="submit" class="btn btn-primary">
                                    <div class="button-content-wrapper ">
                                        <span class="button-text">
                                            {{ __('register_now') }}
                                        </span>
                                    </div>
                                </button>
                            </div>
                        </form>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection

@section('script')
    <script src='https://www.google.com/recaptcha/api.js'></script>
    <script src="https://code.jquery.com/ui/1.13.2/jquery-ui.js"></script>
    <script>
        function LoginService(value) {
            $("#ModalBtn").css("display", "block");
            var action = "auth/" + value + "/redirect";
            $("#LoginFormHit").attr("action", action);
        }

        function CloseModoal() {
            $("#ModalBtn").css("display", "none");
        }
    </script>
    <script>
        function limitDigits(input) {
            const companyRadio = document.getElementById('switcher-toggle-off');
            const maxLength = companyRadio && companyRadio.checked ? 13 : 16;

            if (input.value.length > maxLength) {
                input.value = input.value.slice(0, maxLength);
            }
        }

        function limitPhoneDigits(input) {
            if (input.value.length > 14) {
                input.value = input.value.slice(0, 14);
            }
        }
    </script>
    <script>
        $(document).ready(function() {
            validate();
            $('#name, #email, #password, #password_confirmation, #term').keyup(validate);

            // Inisialisasi datepicker untuk tanggal lahir
            $("#tanggal_lahir").datepicker({
                dateFormat: "d MM yy",
                changeMonth: true,
                changeYear: true,
                yearRange: "-100:+0",
                maxDate: "-18Y", // Minimal umur 18 tahun
                monthNames: [ "Januari", "Februari", "Maret", "April", "Mei", "Juni", "Juli", "Agustus", "September", "Oktober", "November", "Desember" ],
                dayNames: [ "Minggu", "Senin", "Selasa", "Rabu", "Kamis", "Jumat", "Sabtu" ],
                dayNamesMin: [ "Min", "Sen", "Sel", "Rab", "Kam", "Jum", "Sab" ],
                beforeShow: function(input, inst) {
                    // Pastikan datepicker muncul di atas elemen lain
                    setTimeout(function() {
                        inst.dpDiv.css({
                            'z-index': 9999
                        });
                    }, 0);
                }
            });
        });

        function validate() {
            if (
                $('#name').val().length > 0 &&
                $('#email').val().length > 0 &&
                $('#password').val().length > 0 &&
                $('#password_confirmation').val().length > 0 &&
                $('#term').val().length > 0) {
                $('#submitButton').attr('disabled', false);
            } else {
                $('#submitButton').attr('disabled', true);
            }
        }

        function passToText(id, icon) {
            var input = $('#' + id);
            var eyeIcon = $('#' + icon);
            if (input.is('input[type="password"]')) {
                eyeIcon.html('<i class="ph-eye-slash @error('password') m-3 @enderror"></i>');
                input.attr('type', 'text');
            } else {
                eyeIcon.html('<i class="ph-eye @error('password') m-3 @enderror"></i>');
                input.attr('type', 'password');
            }
        }
    </script>
    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const nameInput = document.getElementById('name');
            const nikInput = document.getElementById('nik');  // Ambil field NIK
            const noHpInput = document.getElementById('no_hp'); // Ambil field nomor HP
            const candidateRadio = document.getElementById('switcher-toggle-on');
            const companyRadio = document.getElementById('switcher-toggle-off');
            const nikInfo = document.getElementById('nik-info');
            const namaInfo = document.getElementById('nama-info');
            const ak1Label = document.getElementById('ak1Label'); // Ambil label AK1
            const companyFields = document.querySelectorAll('.company-fields');
            const candidateFields = document.querySelectorAll('.form-pelamar');
            const organizationTypeSelect = document.getElementById('organization_type');
            const fullCompanyNameInput = document.getElementById('full_company_name');
            const registrationForm = document.getElementById('registrationForm');

            // Function to toggle company fields visibility
            function toggleCompanyFields(isCompany) {
                if (isCompany) {
                    nameInput.placeholder = "{{ __('Nama Perusahaan*') }}";
                    nikInput.placeholder = "{{ __('NIB (Nomor Induk Berusaha)*') }}";
                    noHpInput.placeholder = "{{ __('No Telp HRD/Penanggung jawab') }}";
                    document.getElementById('alamat_ktp').placeholder = "{{ __('Alamat Perusahaan') }}";
                    nikInfo.innerText = 'NIB: 13 Digit';
                    namaInfo.innerText = 'Contoh: Sinergi Bersatu (tanpa badan hukum)';
                    ak1Label.innerHTML = `Upload NIB <i>(Nomor Induk Berusaha)</i><br>
                    <small class="text-danger fst-italic">Format: jpg/jpeg/png/pdf. Max 10MB.</small>`;

                    // Show company fields, hide candidate fields
                    companyFields.forEach(field => field.style.display = 'block');
                    candidateFields.forEach(field => field.style.display = 'none');

                    // For companies, show all provinces
                    if (typeof window.updateProvinceOptions === 'function' && window.provincesList) {
                        window.updateProvinceOptions(window.provincesList);
                    }
                } else {
                    nameInput.placeholder = "{{ __('full_name') }}";
                    nikInput.placeholder = "{{ __('nik') }}";
                    noHpInput.placeholder = "{{ __('phone_number') }}";
                    document.getElementById('alamat_ktp').placeholder = "{{ __('address_ktp') }}";
                    nikInfo.innerText = 'NIK: 16 Digit';
                    namaInfo.innerText = '';
                    ak1Label.innerHTML = `Upload Foto <span class="text-danger text-decoration-underline fw-bold">AK1 (Kartu Kuning)</span> atau <span class="text-danger text-decoration-underline fw-bold">KTP</span> (jika belum memiliki kartu kuning)<br>
                        <small class="text-danger fst-italic">Format: jpg/jpeg/png/pdf. Max 10MB.</small>`;

                    // Hide company fields, show candidate fields
                    companyFields.forEach(field => field.style.display = 'none');
                    candidateFields.forEach(field => field.style.display = 'block');

                    // For candidates, show all provinces
                    if (typeof window.updateProvinceOptions === 'function' && window.provincesList) {
                        window.updateProvinceOptions(window.provincesList);
                    }
                }
            }

            // Function to update full company name based on organization type and name
            function updateFullCompanyName() {
                if (companyRadio.checked && organizationTypeSelect.value) {
                    const selectedOption = organizationTypeSelect.options[organizationTypeSelect.selectedIndex];
                    const prefix = selectedOption.getAttribute('data-prefix');
                    const companyName = nameInput.value.trim();

                    if (companyName && prefix) {
                        // Format nama perusahaan dengan prefix badan hukum
                        fullCompanyNameInput.value = prefix + ' ' + companyName;
                    } else {
                        fullCompanyNameInput.value = companyName;
                    }
                } else {
                    fullCompanyNameInput.value = nameInput.value.trim();
                }
            }

            // Set initial state based on old input
            const oldRole = "{{ old('role', 'candidate') }}";
            if (oldRole === 'company') {
                companyRadio.checked = true;
                toggleCompanyFields(true);
            } else {
                candidateRadio.checked = true;
                toggleCompanyFields(false);
            }

            // Change placeholders and info text based on the selected radio button
            candidateRadio.addEventListener('change', function () {
                toggleCompanyFields(false);
                updateFullCompanyName();
            });

            companyRadio.addEventListener('change', function () {
                toggleCompanyFields(true);
                updateFullCompanyName();
            });

            // Update full company name when organization type changes
            organizationTypeSelect.addEventListener('change', updateFullCompanyName);

            // Update full company name when company name changes
            nameInput.addEventListener('input', updateFullCompanyName);

            // Handle form submission
            registrationForm.addEventListener('submit', function(event) {
                if (companyRadio.checked) {
                    updateFullCompanyName();
                }
            });
        });
    </script>
<script>
    document.addEventListener("DOMContentLoaded", function () {
        const provinsiSelect = document.getElementById("provinsi"),
              citySelect = document.getElementById("kabupaten_kota"),
              districtSelect = document.getElementById("kecamatan"),
              villageSelect = document.getElementById("kelurahan"),
              hiddenProvince = document.getElementById("hidden_province"),
              hiddenCity = document.getElementById("hidden_city"),
              hiddenDistrict = document.getElementById("hidden_district"),
              hiddenVillage = document.getElementById("hidden_village"),
              companyRadio = document.getElementById("switcher-toggle-off"),
              candidateRadio = document.getElementById("switcher-toggle-on");

        let provincesList = [];

        // CORS proxy to handle CORS issues
        const corsProxy = 'https://cors-anywhere.herokuapp.com/';

        // Fetch all provinces
        function fetchProvinces() {
            const url = "https://tupski.github.io/api-wilayah-indonesia/api/provinces.json";

            fetch(url)
                .then(response => response.json())
                .then(provinces => {
                    provincesList = provinces;
                    updateProvinceOptions(provincesList);
                    if (candidateRadio.checked) filterProvinceForCandidate();
                })
                .catch(error => {
                    console.error("Error fetching provinces, trying with CORS proxy:", error);
                    // Try with CORS proxy if direct fetch fails
                    fetch(corsProxy + url)
                        .then(response => response.json())
                        .then(provinces => {
                            provincesList = provinces;
                            updateProvinceOptions(provincesList);
                            if (candidateRadio.checked) filterProvinceForCandidate();
                        })
                        .catch(proxyError => console.error("Error fetching provinces with proxy:", proxyError));
                });
        }

        // Update province dropdown options
        function updateProvinceOptions(provinces) {
            provinsiSelect.innerHTML = '<option value="">Pilih Provinsi</option>';
            provinces.forEach(province => {
                const option = document.createElement("option");
                const capitalizedName = capitalize(province.name);
                option.value = capitalizedName; // Simpan nama dengan kapitalisasi yang benar
                option.textContent = capitalizedName;
                option.dataset.id = province.id; // Simpan ID sebagai data attribute untuk fetch data berikutnya
                provinsiSelect.appendChild(option);
            });
        }

        // Fetch cities based on province selection
        function fetchCities(provinceName) {
            if (!provinceName) return;

            // Save the province name to hidden field with proper capitalization
            hiddenProvince.value = capitalize(provinceName);

            // Get the province ID from the selected option's data attribute
            const selectedOption = provinsiSelect.options[provinsiSelect.selectedIndex];
            const provinceId = selectedOption ? selectedOption.dataset.id : "";
            if (!provinceId) return;

            const url = `https://tupski.github.io/api-wilayah-indonesia/api/regencies/${provinceId}.json`;

            fetch(url)
                .then(response => response.json())
                .then(cities => {
                    populateOptions(citySelect, cities, "Pilih Kab/Kota");
                    // No filtering needed for candidates anymore
                })
                .catch(error => {
                    console.error("Error fetching cities, trying with CORS proxy:", error);
                    // Try with CORS proxy if direct fetch fails
                    fetch(corsProxy + url)
                        .then(response => response.json())
                        .then(cities => {
                            populateOptions(citySelect, cities, "Pilih Kab/Kota");
                            // No filtering needed for candidates anymore
                        })
                        .catch(proxyError => {
                            console.error("Error fetching cities with proxy:", proxyError);
                            citySelect.innerHTML = '<option value="">Pilih Kab/Kota</option>';
                        });
                });
        }

        // Fetch districts based on city selection
        function fetchDistricts(cityName) {
            if (!cityName) return;

            // Save the city name to hidden field with proper capitalization
            hiddenCity.value = capitalize(cityName);

            // Get the city ID from the selected option's data attribute
            const selectedOption = citySelect.options[citySelect.selectedIndex];
            const cityId = selectedOption ? selectedOption.dataset.id : "";
            if (!cityId) return;

            const url = `https://tupski.github.io/api-wilayah-indonesia/api/districts/${cityId}.json`;

            fetch(url)
                .then(response => response.json())
                .then(districts => populateOptions(districtSelect, districts, "Pilih Kecamatan"))
                .catch(error => {
                    console.error("Error fetching districts, trying with CORS proxy:", error);
                    // Try with CORS proxy if direct fetch fails
                    fetch(corsProxy + url)
                        .then(response => response.json())
                        .then(districts => populateOptions(districtSelect, districts, "Pilih Kecamatan"))
                        .catch(proxyError => {
                            console.error("Error fetching districts with proxy:", proxyError);
                            districtSelect.innerHTML = '<option value="">Pilih Kecamatan</option>';
                        });
                });
        }

        // Fetch villages based on district selection
        function fetchVillages(districtName) {
            if (!districtName) return;

            // Save the district name to hidden field with proper capitalization
            hiddenDistrict.value = capitalize(districtName);

            // Get the district ID from the selected option's data attribute
            const selectedOption = districtSelect.options[districtSelect.selectedIndex];
            const districtId = selectedOption ? selectedOption.dataset.id : "";
            if (!districtId) return;

            const url = `https://tupski.github.io/api-wilayah-indonesia/api/villages/${districtId}.json`;

            fetch(url)
                .then(response => response.json())
                .then(villages => populateOptions(villageSelect, villages, "Pilih Kelurahan"))
                .catch(error => {
                    console.error("Error fetching villages, trying with CORS proxy:", error);
                    // Try with CORS proxy if direct fetch fails
                    fetch(corsProxy + url)
                        .then(response => response.json())
                        .then(villages => populateOptions(villageSelect, villages, "Pilih Kelurahan"))
                        .catch(proxyError => {
                            console.error("Error fetching villages with proxy:", proxyError);
                            villageSelect.innerHTML = '<option value="">Pilih Kelurahan</option>';
                        });
                });
        }

        // Save village name when selected
        function saveVillage(villageName) {
            if (!villageName) return;

            // Save the village name to hidden field with proper capitalization
            hiddenVillage.value = capitalize(villageName);
        }

        // Populate dropdown options
        function populateOptions(selectElement, items, defaultLabel) {
            selectElement.innerHTML = `<option value="">${defaultLabel}</option>`;
            items.forEach(item => {
                const option = document.createElement("option");
                const capitalizedName = capitalize(item.name);
                option.value = capitalizedName; // Simpan nama dengan kapitalisasi yang benar
                option.textContent = capitalizedName;
                option.dataset.id = item.id; // Simpan ID sebagai data attribute untuk fetch data berikutnya
                selectElement.appendChild(option);
            });
        }

        // Capitalize first letter of each word
        function capitalize(str) {
            if (!str) return '';
            return str.toLowerCase().replace(/\b\w/g, function(char) {
                return char.toUpperCase();
            });
        }

        // Function for candidates to show all provinces (no filtering)
        function filterProvinceForCandidate() {
            // Show all provinces for candidates
            updateProvinceOptions(provincesList);
        }

        // Function for candidates to show all cities (no filtering)
        function filterCitiesForCandidate() {
            // No filtering needed, all cities will be shown by the fetchCities function
            // This function is kept for backward compatibility
        }

        // Event listeners for dropdowns
        provinsiSelect.addEventListener("change", function() {
            fetchCities(this.value);
        });

        citySelect.addEventListener("change", function() {
            fetchDistricts(this.value);
        });

        districtSelect.addEventListener("change", function() {
            fetchVillages(this.value);
        });

        villageSelect.addEventListener("change", function() {
            // Save the village name to hidden field with proper capitalization
            const selectedOption = villageSelect.options[villageSelect.selectedIndex];
            hiddenVillage.value = selectedOption ? capitalize(selectedOption.value) : "";
        });

        // Event listeners for user type radio buttons
        companyRadio.addEventListener("change", function() {
            // For companies, show all provinces
            updateProvinceOptions(provincesList);

            // Reset other dropdowns
            citySelect.innerHTML = '<option value="">Pilih Kab/Kota</option>';
            districtSelect.innerHTML = '<option value="">Pilih Kecamatan</option>';
            villageSelect.innerHTML = '<option value="">Pilih Kelurahan</option>';

            // Clear hidden fields
            hiddenProvince.value = "";
            hiddenCity.value = "";
            hiddenDistrict.value = "";
            hiddenVillage.value = "";
        });

        candidateRadio.addEventListener("change", function() {
            // For candidates, show all provinces (same as companies)
            updateProvinceOptions(provincesList);

            // Reset other dropdowns
            citySelect.innerHTML = '<option value="">Pilih Kab/Kota</option>';
            districtSelect.innerHTML = '<option value="">Pilih Kecamatan</option>';
            villageSelect.innerHTML = '<option value="">Pilih Kelurahan</option>';

            // Clear hidden fields
            hiddenProvince.value = "";
            hiddenCity.value = "";
            hiddenDistrict.value = "";
            hiddenVillage.value = "";
        });

        // Initialize
        fetchProvinces();

        // Make functions and data globally available
        window.fetchCities = fetchCities;
        window.fetchDistricts = fetchDistricts;
        window.fetchVillages = fetchVillages;
        window.updateProvinceOptions = updateProvinceOptions;
        window.filterProvinceForCandidate = filterProvinceForCandidate;
        window.provincesList = provincesList;
    });
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function() {
            const form = document.getElementById('registrationForm');
            if (!form) return;

            const fields = {
                name: document.getElementById('name'),
                nik: document.getElementById('nik'),
                provinsi: document.getElementById('provinsi'),
                kabupaten_kota: document.getElementById('kabupaten_kota'),
                kecamatan: document.getElementById('kecamatan'),
                kelurahan: document.getElementById('kelurahan'),
                alamat_ktp: document.getElementById('alamat_ktp'),
                tempat_lahir: document.getElementById('tempat_lahir'),
                tanggal_lahir: document.getElementById('tanggal_lahir'),
                jenis_kelamin: document.getElementById('jenis_kelamin'),
                status_perkawinan: document.getElementById('status_perkawinan'),
                agama: document.getElementById('agama'),
                pendidikan_terakhir: document.getElementById('pendidikan_terakhir'),
                nama_hrd: document.getElementById('nama_hrd'),
                jabatan: document.getElementById('jabatan'),
                no_hp: document.getElementById('no_hp'),
                email: document.getElementById('email'),
                password: document.getElementById('password'),
            };

            const errorFields = {
                nameError: document.getElementById('nameError'),
                nikError: document.getElementById('nikError'),
                provinsiError: document.getElementById('provinsiError'),
                kabupatenKotaError: document.getElementById('kabupatenKotaError'),
                kecamatanError: document.getElementById('kecamatanError'),
                kelurahanError: document.getElementById('kelurahanError'),
                alamatKtpError: document.getElementById('alamatKtpError'),
                tempatLahirError: document.getElementById('tempatLahirError'),
                tanggalLahirError: document.getElementById('tanggalLahirError'),
                jenisKelaminError: document.getElementById('jenisKelaminError'),
                statusPerkawinanError: document.getElementById('statusPerkawinanError'),
                agamaError: document.getElementById('agamaError'),
                pendidikanError: document.getElementById('pendidikanError'),
                namaHrdError: document.getElementById('namaHrdError'),
                jabatanError: document.getElementById('jabatanError'),
                nomorTeleponError: document.getElementById('nomorTeleponError'),
                emailError: document.getElementById('emailError'),
                passwordError: document.getElementById('passwordError'),
            };

            function validateField(field, errorField, pattern, minLength, maxLength, required = true) {
                if (!field || !errorField) return;

                field.addEventListener('input', function() {
                    const value = field.value;
                    let error = '';

                    if (required && !value) {
                        error = 'Kolom ini dibutuhkan.';
                    } else if (pattern && !pattern.test(value)) {
                        error = 'Format salah!';
                    } else if (minLength && value.length < minLength) {
                        error = `Jumlah karakter minimal ${minLength} karakter.`;
                    } else if (maxLength && value.length > maxLength) {
                        error = `Jumlah karakter maksimal ${maxLength} karakter.`;
                    }

                    if (error) {
                        field.classList.add('is-invalid');
                        errorField.textContent = error;
                    } else {
                        field.classList.remove('is-invalid');
                        errorField.textContent = '';
                    }
                });
            }

            function validatePhoneNumber() {
                const phoneField = fields.no_hp;
                const phoneError = errorFields.nomorTeleponError;

                phoneField.addEventListener('input', function() {
                    const value = phoneField.value.replace(/\D/g, ''); // Remove non-numeric characters
                    let error = '';

                    if (!value) {
                        error = 'Nomor telepon is required.';
                    } else if (!/^\d+$/.test(value)) {
                        error = 'Invalid format.';
                    } else {
                        // Check if it starts with 08 and format to 628
                        if (value.startsWith('08')) {
                            phoneField.value = '62' + value.substring(1);
                        }
                        // Check if it starts with 02 and format to 622
                        else if (value.startsWith('02')) {
                            phoneField.value = '62' + value.substring(1);
                        }
                    }

                    // Display error message or clear it
                    if (error) {
                        phoneField.classList.add('is-invalid');
                        phoneError.textContent = error;
                    } else {
                        phoneField.classList.remove('is-invalid');
                        phoneError.textContent = '';
                    }
                });
            }


            validateField(fields.name, errorFields.nameError, /^[\pL\s.]+$/, null, null);
            validateField(fields.nik, errorFields.nikError, /^\d{13,16}$/, 13, 16);
            validateField(fields.provinsi, errorFields.provinsiError, null, null, null, true);
            validateField(fields.kabupaten_kota, errorFields.kabupatenKotaError, null, null, null, true);
            validateField(fields.kecamatan, errorFields.kecamatanError, null, null, null, true);
            validateField(fields.kelurahan, errorFields.kelurahanError, null, null, null, false);
            validateField(fields.alamat_ktp, errorFields.alamatKtpError, null, null, null, true);
            validateField(fields.tempat_lahir, errorFields.tempatLahirError, /^[\pL\s.]+$/, null, null, false);
            validateField(fields.tanggal_lahir, errorFields.tanggalLahirError, null, null, null, false);
            validateField(fields.jenis_kelamin, errorFields.jenisKelaminError, null, null, null, false);
            validateField(fields.status_perkawinan, errorFields.statusPerkawinanError, null, null, null, false);
            validateField(fields.agama, errorFields.agamaError, null, null, null, false);
            validateField(fields.pendidikan_terakhir, errorFields.pendidikanError, null, null, null, false);
            validatePhoneNumber();
        });
    </script>

    <script>
        document.addEventListener('DOMContentLoaded', function () {
            const switcherOn = document.getElementById('switcher-toggle-on');
            const switcherOff = document.getElementById('switcher-toggle-off');
            const candidateFields = document.getElementById('candidateFields');
            const companyFields = document.getElementById('companyFields');
            const submitButton = document.getElementById('submitButton');

            // Handle switch between candidate and company
            switcherOn.addEventListener('change', function () {
                candidateFields.classList.remove('d-none');
                companyFields.classList.add('d-none');
                submitButton.disabled = false;
            });

            switcherOff.addEventListener('change', function () {
                candidateFields.classList.add('d-none');
                companyFields.classList.remove('d-none');
                submitButton.disabled = false;
            });

            // Check URL parameters for automatic toggle
            const urlParams = new URLSearchParams(window.location.search);
            const userType = urlParams.get('user');

            // Normalize user type to lowercase for case-insensitive comparison
            const normalizedUserType = userType ? userType.toLowerCase() : '';

            // Map pencaker to candidate and perusahaan to company
            if (normalizedUserType === 'perusahaan' || normalizedUserType === 'company' || normalizedUserType === 'employer') {
                // Set the value to 'company' for backend processing
                switcherOff.value = 'company';
                switcherOff.checked = true;
                switcherOff.dispatchEvent(new Event('change'));

                // Show company fields
                const companyFieldsElements = document.querySelectorAll('.company-fields');
                companyFieldsElements.forEach(function(element) {
                    element.style.display = 'block';
                });

                // Hide candidate fields
                const candidateFieldsElements = document.querySelectorAll('.form-pelamar');
                candidateFieldsElements.forEach(function(element) {
                    element.style.display = 'none';
                });

                // Update placeholders
                document.getElementById('name').placeholder = "{{ __('Nama Perusahaan*') }}";
                document.getElementById('nik').placeholder = "{{ __('NIB (Nomor Induk Berusaha)*') }}";
                document.getElementById('no_hp').placeholder = "{{ __('No Telp HRD/Penanggung jawab') }}";
                document.getElementById('alamat_ktp').placeholder = "{{ __('Alamat Perusahaan') }}";
                document.getElementById('nik-info').innerText = 'NIB: 13 Digit';
                document.getElementById('nama-info').innerText = 'Contoh: Sinergi Bersatu (tanpa badan hukum)';
                document.getElementById('ak1Label').innerHTML = `Upload NIB <i>(Nomor Induk Berusaha)</i><br>
                <small class="text-danger fst-italic">Format: jpg/jpeg/png/pdf. Max 10MB.</small>`;
            } else if (normalizedUserType === 'pencaker' || normalizedUserType === 'candidate' || normalizedUserType === 'jobseeker') {
                switcherOn.checked = true;
                switcherOn.dispatchEvent(new Event('change'));

                // Show candidate fields
                const candidateFieldsElements = document.querySelectorAll('.form-pelamar');
                candidateFieldsElements.forEach(function(element) {
                    element.style.display = 'block';
                });

                // Hide company fields
                const companyFieldsElements = document.querySelectorAll('.company-fields');
                companyFieldsElements.forEach(function(element) {
                    element.style.display = 'none';
                });
            }
        });
    </script>
@endsection
